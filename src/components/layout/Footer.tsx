import Link from 'next/link'

interface FooterProps {
  site: {
    name: string
    slug: string
  }
  links?: {
    [section: string]: Array<{
      label: string
      href: string
    }>
  }
}

export function Footer({ site, links = {} }: FooterProps) {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="border-t bg-muted/50">
      <div className="site-width py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Site info */}
          <div className="md:col-span-1">
            <h3 className="font-semibold text-lg mb-4">{site.name}</h3>
            <p className="text-sm text-muted-foreground">
              Stay updated with the latest news and insights.
            </p>
          </div>

          {/* Link sections */}
          {Object.entries(links).map(([section, sectionLinks]) => (
            <div key={section}>
              <h4 className="font-medium mb-4">{section}</h4>
              <ul className="space-y-2">
                {sectionLinks.map((link) => (
                  <li key={link.href}>
                    <Link
                      href={link.href}
                      className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                    >
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <div className="mt-8 pt-8 border-t">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-muted-foreground">
              © {currentYear} {site.name}. All rights reserved.
            </p>
            <div className="flex items-center space-x-4 mt-4 md:mt-0">
              <Link
                href="/privacy"
                className="text-sm text-muted-foreground hover:text-foreground transition-colors"
              >
                Privacy Policy
              </Link>
              <Link
                href="/terms"
                className="text-sm text-muted-foreground hover:text-foreground transition-colors"
              >
                Terms of Service
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
