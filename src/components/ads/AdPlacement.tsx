import { cn } from '@/lib/utils'
import Image from 'next/image'
import Link from 'next/link'

interface AdCreative {
  id: string
  type: 'text-ad' | 'banner-image' | 'text-link'
  destinationUrl: string
  utmSource?: string
  utmMedium?: string
  utmCampaign?: string
  
  // Text ad content
  textContent?: {
    headline: string
    description: string
  }
  
  // Banner content
  bannerContent?: {
    bannerImage: {
      url: string
      alt: string
      width?: number
      height?: number
    }
    altText: string
  }
  
  // Text link content
  textLinkContent?: {
    linkText: string
    linkDescription: string
  }
}

interface AdPlacementProps {
  creative: AdCreative
  placement: 'header' | 'sidebar' | 'content' | 'footer'
  caption?: string
  className?: string
}

export function AdPlacement({ creative, placement, caption, className }: AdPlacementProps) {
  // Build tracking URL with UTM parameters
  const trackingUrl = new URL(creative.destinationUrl)
  if (creative.utmSource) trackingUrl.searchParams.set('utm_source', creative.utmSource)
  if (creative.utmMedium) trackingUrl.searchParams.set('utm_medium', creative.utmMedium)
  if (creative.utmCampaign) trackingUrl.searchParams.set('utm_campaign', creative.utmCampaign)

  const renderCreative = () => {
    switch (creative.type) {
      case 'text-ad':
        return (
          <div className="p-4">
            <h3 className="font-semibold text-sm mb-2">{creative.textContent?.headline}</h3>
            <p className="text-sm text-muted-foreground">{creative.textContent?.description}</p>
          </div>
        )

      case 'banner-image':
        return (
          <div className="relative">
            <Image
              src={creative.bannerContent?.bannerImage.url || ''}
              alt={creative.bannerContent?.altText || ''}
              width={creative.bannerContent?.bannerImage.width || 728}
              height={creative.bannerContent?.bannerImage.height || 90}
              className="w-full h-auto"
            />
          </div>
        )

      case 'text-link':
        return (
          <div className="p-4">
            <h3 className="font-semibold text-sm mb-2 text-brand-600 dark:text-brand-400">
              {creative.textLinkContent?.linkText}
            </h3>
            <p className="text-sm text-muted-foreground">{creative.textLinkContent?.linkDescription}</p>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className={cn('ad-container', className)}>
      <div className="ad-label">AD</div>
      <Link
        href={trackingUrl.toString()}
        target="_blank"
        rel="noopener noreferrer sponsored"
        className="block hover:opacity-90 transition-opacity"
      >
        {renderCreative()}
      </Link>
      {caption && <div className="ad-caption">{caption}</div>}
    </div>
  )
}

// Placeholder component for when no ads are available
export function AdPlaceholder({ placement, className }: { placement: string; className?: string }) {
  return (
    <div className={cn('ad-container', className)}>
      <div className="ad-label">AD</div>
      <div className="p-4 text-center">
        <p className="text-sm text-muted-foreground">Advertisement space available</p>
      </div>
    </div>
  )
}
