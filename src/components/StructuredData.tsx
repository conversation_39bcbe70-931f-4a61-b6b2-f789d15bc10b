import Script from 'next/script'

interface TenantInfo {
  id: string
  name: string
  domain: string
  description?: string
  logo?: {
    url: string
  }
}

interface ContentItem {
  id: string
  title: string
  slug: string
  excerpt?: string
  publishedAt?: string
  updatedAt: string
  author?: {
    name: string
    email: string
    bio?: string
  }
  categories?: Array<{
    name: string
    slug: string
  }>
  tags?: Array<{
    name: string
    slug: string
  }>
  featuredImage?: {
    url: string
    alt?: string
  }
}

interface StructuredDataProps {
  tenant: TenantInfo
  content?: ContentItem
  type: 'website' | 'article' | 'organization' | 'breadcrumb'
  breadcrumbs?: Array<{
    name: string
    url: string
  }>
}

export default function StructuredData({ tenant, content, type, breadcrumbs }: StructuredDataProps) {
  const baseUrl = `https://${tenant.domain}`

  const generateOrganizationSchema = () => ({
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: tenant.name,
    url: baseUrl,
    description: tenant.description,
    logo: tenant.logo?.url ? {
      '@type': 'ImageObject',
      url: tenant.logo.url,
    } : undefined,
    sameAs: [],
  })

  const generateWebsiteSchema = () => ({
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: tenant.name,
    url: baseUrl,
    description: tenant.description,
    publisher: generateOrganizationSchema(),
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: `${baseUrl}/search?q={search_term_string}`,
      },
      'query-input': 'required name=search_term_string',
    },
  })

  const generateArticleSchema = () => {
    if (!content || !content.publishedAt) return null

    return {
      '@context': 'https://schema.org',
      '@type': 'Article',
      headline: content.title,
      description: content.excerpt,
      url: `${baseUrl}/${content.slug}`,
      datePublished: content.publishedAt,
      dateModified: content.updatedAt,
      author: content.author ? {
        '@type': 'Person',
        name: content.author.name,
        description: content.author.bio,
      } : undefined,
      publisher: generateOrganizationSchema(),
      mainEntityOfPage: {
        '@type': 'WebPage',
        '@id': `${baseUrl}/${content.slug}`,
      },
      image: content.featuredImage ? {
        '@type': 'ImageObject',
        url: content.featuredImage.url,
        alt: content.featuredImage.alt || content.title,
      } : undefined,
      articleSection: content.categories?.map(cat => cat.name),
      keywords: content.tags?.map(tag => tag.name).join(', '),
    }
  }

  const generateBreadcrumbSchema = () => {
    if (!breadcrumbs || breadcrumbs.length === 0) return null

    return {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      itemListElement: breadcrumbs.map((crumb, index) => ({
        '@type': 'ListItem',
        position: index + 1,
        name: crumb.name,
        item: crumb.url,
      })),
    }
  }

  const getSchema = () => {
    switch (type) {
      case 'website':
        return generateWebsiteSchema()
      case 'article':
        return generateArticleSchema()
      case 'organization':
        return generateOrganizationSchema()
      case 'breadcrumb':
        return generateBreadcrumbSchema()
      default:
        return null
    }
  }

  const schema = getSchema()
  if (!schema) return null

  return (
    <Script
      id={`structured-data-${type}`}
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(schema),
      }}
    />
  )
}
