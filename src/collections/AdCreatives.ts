import type { CollectionConfig } from 'payload'

export const AdCreatives: CollectionConfig = {
  slug: 'ad-creatives',
  labels: {
    singular: 'Ad Creative',
    plural: 'Ad Creatives',
  },
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'campaign', 'type', 'status', 'updatedAt'],
    group: 'Sponsorship',
  },
  access: {
    // Sponsors can only see their own creatives, admins can see all
    read: ({ req: { user } }) => {
      if (!user) return false
      if (user.roles?.includes('super-admin') || user.roles?.includes('admin')) {
        return true
      }
      if (user.roles?.includes('sponsor')) {
        return {
          'campaign.sponsor': {
            equals: user.id,
          },
        }
      }
      return false
    },
    create: ({ req: { user } }) => {
      return user?.roles?.includes('sponsor') || user?.roles?.includes('super-admin') || user?.roles?.includes('admin') || false
    },
    update: ({ req: { user } }) => {
      if (user?.roles?.includes('super-admin') || user?.roles?.includes('admin')) {
        return true
      }
      if (user?.roles?.includes('sponsor')) {
        return {
          'campaign.sponsor': {
            equals: user.id,
          },
        }
      }
      return false
    },
    delete: ({ req: { user } }) => {
      if (user?.roles?.includes('super-admin') || user?.roles?.includes('admin')) {
        return true
      }
      if (user?.roles?.includes('sponsor')) {
        return {
          'campaign.sponsor': {
            equals: user.id,
          },
        }
      }
      return false
    },
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      label: 'Creative Title',
      admin: {
        description: 'Internal title for this ad creative',
      },
    },
    {
      name: 'campaign',
      type: 'relationship',
      relationTo: 'ad-campaigns',
      required: true,
      label: 'Campaign',
      admin: {
        description: 'Which campaign this creative belongs to',
      },
    },
    {
      name: 'type',
      type: 'select',
      required: true,
      options: [
        {
          label: 'Text Ad',
          value: 'text',
        },
        {
          label: 'Banner Image',
          value: 'banner',
        },
        {
          label: 'Text + Link',
          value: 'text-link',
        },
      ],
      admin: {
        description: 'Type of advertisement',
      },
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      defaultValue: 'draft',
      options: [
        {
          label: 'Draft',
          value: 'draft',
        },
        {
          label: 'Pending Approval',
          value: 'pending',
        },
        {
          label: 'Approved',
          value: 'approved',
        },
        {
          label: 'Rejected',
          value: 'rejected',
        },
      ],
      admin: {
        description: 'Approval status of this creative',
      },
      access: {
        update: ({ req: { user } }) => {
          // Only admins can change approval status
          if (user?.roles?.includes('super-admin') || user?.roles?.includes('admin')) {
            return true
          }
          return false
        },
      },
    },
    // Text Ad Fields
    {
      name: 'textContent',
      type: 'group',
      label: 'Text Content',
      admin: {
        condition: (data) => data?.type === 'text' || data?.type === 'text-link',
        description: 'Content for text-based advertisements',
      },
      fields: [
        {
          name: 'headline',
          type: 'text',
          required: true,
          label: 'Headline',
          maxLength: 60,
          admin: {
            description: 'Main headline (max 60 characters)',
          },
        },
        {
          name: 'description',
          type: 'textarea',
          label: 'Description',
          maxLength: 200,
          admin: {
            description: 'Additional description text (max 200 characters)',
          },
        },
        {
          name: 'callToAction',
          type: 'text',
          label: 'Call to Action',
          maxLength: 20,
          admin: {
            description: 'Button text (max 20 characters, e.g., "Learn More")',
          },
        },
      ],
    },
    // Banner Ad Fields
    {
      name: 'bannerContent',
      type: 'group',
      label: 'Banner Content',
      admin: {
        condition: (data) => data?.type === 'banner',
        description: 'Content for banner advertisements',
      },
      fields: [
        {
          name: 'image',
          type: 'upload',
          relationTo: 'media',
          required: true,
          label: 'Banner Image',
          admin: {
            description: 'Banner image (recommended: 728x90, 300x250, or 320x50)',
          },
        },
        {
          name: 'altText',
          type: 'text',
          required: true,
          label: 'Alt Text',
          admin: {
            description: 'Alternative text for accessibility',
          },
        },
      ],
    },
    // Link and Tracking
    {
      name: 'link',
      type: 'group',
      label: 'Link & Tracking',
      fields: [
        {
          name: 'url',
          type: 'text',
          required: true,
          label: 'Destination URL',
          admin: {
            description: 'Where users go when they click the ad',
          },
        },
        {
          name: 'utmSource',
          type: 'text',
          label: 'UTM Source',
          defaultValue: 'craftpress',
          admin: {
            description: 'UTM source for tracking (auto-filled)',
          },
        },
        {
          name: 'utmMedium',
          type: 'text',
          label: 'UTM Medium',
          defaultValue: 'sponsored',
          admin: {
            description: 'UTM medium for tracking (auto-filled)',
          },
        },
        {
          name: 'utmCampaign',
          type: 'text',
          label: 'UTM Campaign',
          admin: {
            description: 'UTM campaign for tracking (optional)',
          },
        },
      ],
    },
    // Admin Notes
    {
      name: 'adminNotes',
      type: 'textarea',
      label: 'Admin Notes',
      admin: {
        description: 'Internal notes about this creative (not visible to sponsor)',
      },
      access: {
        read: ({ req: { user } }) => {
          return user?.roles?.includes('super-admin') || user?.roles?.includes('admin') || false
        },
        update: ({ req: { user } }) => {
          return user?.roles?.includes('super-admin') || user?.roles?.includes('admin') || false
        },
      },
    },
  ],
  timestamps: true,
}
