import type { CollectionConfig } from 'payload'

export const Tenants: CollectionConfig = {
  slug: 'tenants',
  labels: {
    singular: 'Site',
    plural: 'Sites',
  },
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'slug', 'domain', 'isActive', 'updatedAt'],
  },
  access: {
    // Restrict tenant management to super admins only
    read: ({ req: { user } }) => {
      if (!user) return false
      // Allow super admins to see all tenants
      return user.roles?.includes('super-admin') || false
    },
    create: ({ req: { user } }) => {
      return user?.roles?.includes('super-admin') || false
    },
    update: ({ req: { user } }) => {
      return user?.roles?.includes('super-admin') || false
    },
    delete: ({ req: { user } }) => {
      return user?.roles?.includes('super-admin') || false
    },
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      label: 'Site Name',
      admin: {
        description: 'Display name for this site/tenant',
      },
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
      label: 'Site Slug',
      admin: {
        description: 'URL-friendly identifier (e.g., "my-blog")',
      },
      hooks: {
        beforeValidate: [
          ({ value, originalDoc, data }) => {
            if (data?.name && !value) {
              return data.name
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/(^-|-$)/g, '')
            }
            return value
          },
        ],
      },
    },
    {
      name: 'description',
      type: 'textarea',
      label: 'Site Description',
      admin: {
        description: 'Brief description of your website',
      },
    },
    {
      name: 'domain',
      type: 'text',
      required: true,
      unique: true,
      label: 'Primary Domain',
      admin: {
        description: 'Primary domain for this site (e.g., "myblog.com")',
      },
    },
    {
      name: 'domains',
      type: 'array',
      label: 'Additional Domains',
      admin: {
        description: 'Additional domains/aliases for this site',
      },
      fields: [
        {
          name: 'domain',
          type: 'text',
          required: true,
          label: 'Domain',
        },
      ],
    },
    {
      name: 'isActive',
      type: 'checkbox',
      label: 'Active Site',
      defaultValue: true,
      admin: {
        description: 'Whether this site is active and accessible',
      },
    },
    {
      name: 'themeConfig',
      type: 'group',
      label: 'Theme Configuration',
      fields: [
        {
          name: 'primaryColor',
          type: 'text',
          label: 'Primary Color',
          admin: {
            description: 'Hex color code (e.g., #3B82F6)',
          },
          validate: (val: any) => {
            if (val && !/^#[0-9A-F]{6}$/i.test(val)) {
              return 'Please enter a valid hex color code (e.g., #3B82F6)'
            }
            return true
          },
        },
        {
          name: 'logo',
          type: 'upload',
          relationTo: 'media',
          label: 'Site Logo',
        },
        {
          name: 'favicon',
          type: 'upload',
          relationTo: 'media',
          label: 'Favicon',
        },
        {
          name: 'fontFamily',
          type: 'select',
          label: 'Font Family',
          defaultValue: 'inter',
          options: [
            {
              label: 'Inter',
              value: 'inter',
            },
            {
              label: 'Geist',
              value: 'geist',
            },
            {
              label: 'IBM Plex Serif',
              value: 'ibm-plex-serif',
            },
          ],
          admin: {
            description: 'Choose the primary font family for your site',
          },
        },
      ],
    },
    {
      name: 'seoDefaults',
      type: 'group',
      label: 'SEO Defaults',
      fields: [
        {
          name: 'title',
          type: 'text',
          label: 'Default Site Title',
        },
        {
          name: 'description',
          type: 'textarea',
          label: 'Default Meta Description',
        },

        {
          name: 'ogImage',
          type: 'upload',
          relationTo: 'media',
          label: 'Default Social Share Image',
        },
      ],
    },

    // Social Media
    {
      name: 'social',
      type: 'group',
      label: 'Social Media',
      fields: [
        {
          name: 'twitter',
          type: 'text',
          label: 'Twitter URL',
        },
        {
          name: 'facebook',
          type: 'text',
          label: 'Facebook URL',
        },
        {
          name: 'linkedin',
          type: 'text',
          label: 'LinkedIn URL',
        },
        {
          name: 'instagram',
          type: 'text',
          label: 'Instagram URL',
        },
        {
          name: 'youtube',
          type: 'text',
          label: 'YouTube URL',
        },
        {
          name: 'github',
          type: 'text',
          label: 'GitHub URL',
        },
        {
          name: 'reddit',
          type: 'text',
          label: 'Reddit URL',
        },
        {
          name: 'pinterest',
          type: 'text',
          label: 'Pinterest URL',
        },
        {
          name: 'bluesky',
          type: 'text',
          label: 'Bluesky URL',
        },
      ],
    },
    // Analytics & Tracking
    {
      name: 'analytics',
      type: 'group',
      label: 'Analytics & Tracking',
      fields: [
        {
          name: 'umamiSiteId',
          type: 'text',
          label: 'Umami Site ID',
          admin: {
            description: 'Your Umami analytics site ID',
          },
        },
        {
          name: 'umamiScriptUrl',
          type: 'text',
          label: 'Umami Script URL',
          admin: {
            description: 'The URL to your Umami tracking script',
          },
        },
      ],
    },
  ],
  timestamps: true,
}
