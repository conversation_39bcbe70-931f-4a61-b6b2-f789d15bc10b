import type { CollectionConfig } from 'payload'

export const Users: CollectionConfig = {
  slug: 'users',
  admin: {
    useAsTitle: 'email',
    defaultColumns: ['email', 'roles', 'updatedAt'],
  },
  auth: true,
  fields: [
    // Email added by default
    {
      name: 'roles',
      type: 'select',
      hasMany: true,
      options: [
        {
          label: 'Super Admin',
          value: 'super-admin',
        },
        {
          label: 'Admin',
          value: 'admin',
        },
        {
          label: 'Editor',
          value: 'editor',
        },
        {
          label: 'Author',
          value: 'author',
        },
        {
          label: 'Sponsor',
          value: 'sponsor',
        },
      ],
      defaultValue: ['author'],
      required: true,
      access: {
        // Only super admins can modify roles
        update: ({ req: { user } }) => {
          return user?.roles?.includes('super-admin') || false
        },
      },
    },
    // Sponsor-specific fields
    {
      name: 'sponsorProfile',
      type: 'group',
      label: 'Sponsor Profile',
      admin: {
        condition: (data) => data?.roles?.includes('sponsor'),
        description: 'Additional information for sponsor accounts',
      },
      fields: [
        {
          name: 'companyName',
          type: 'text',
          label: 'Company Name',
          admin: {
            description: 'Official company name for billing and contracts',
          },
        },
        {
          name: 'website',
          type: 'text',
          label: 'Company Website',
          admin: {
            description: 'Company website URL',
          },
        },
        {
          name: 'contactPerson',
          type: 'text',
          label: 'Contact Person',
          admin: {
            description: 'Primary contact person for campaigns',
          },
        },
        {
          name: 'phone',
          type: 'text',
          label: 'Phone Number',
          admin: {
            description: 'Contact phone number',
          },
        },
        {
          name: 'billingAddress',
          type: 'group',
          label: 'Billing Address',
          fields: [
            {
              name: 'street',
              type: 'text',
              label: 'Street Address',
            },
            {
              name: 'city',
              type: 'text',
              label: 'City',
            },
            {
              name: 'state',
              type: 'text',
              label: 'State/Province',
            },
            {
              name: 'zipCode',
              type: 'text',
              label: 'ZIP/Postal Code',
            },
            {
              name: 'country',
              type: 'text',
              label: 'Country',
            },
          ],
        },
        {
          name: 'isApproved',
          type: 'checkbox',
          label: 'Approved Sponsor',
          defaultValue: false,
          admin: {
            description: 'Whether this sponsor is approved for auto-approval of campaigns',
          },
          access: {
            update: ({ req: { user } }) => {
              return user?.roles?.includes('super-admin') || user?.roles?.includes('admin') || false
            },
          },
        },
        {
          name: 'notes',
          type: 'textarea',
          label: 'Internal Notes',
          admin: {
            description: 'Internal notes about this sponsor (not visible to sponsor)',
          },
          access: {
            read: ({ req: { user } }) => {
              return user?.roles?.includes('super-admin') || user?.roles?.includes('admin') || false
            },
            update: ({ req: { user } }) => {
              return user?.roles?.includes('super-admin') || user?.roles?.includes('admin') || false
            },
          },
        },
      ],
    },
    // The tenants array field will be added automatically by the multi-tenant plugin
  ],
}
