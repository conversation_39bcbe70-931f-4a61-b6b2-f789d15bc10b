import type { CollectionConfig } from 'payload'

export const PricingRules: CollectionConfig = {
  slug: 'pricing-rules',
  labels: {
    singular: 'Pricing Rule',
    plural: 'Pricing Rules',
  },
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'site', 'placementType', 'basePrice', 'isActive'],
    group: 'Sponsorship',
  },
  access: {
    // Only admins can manage pricing
    read: ({ req: { user } }) => {
      return user?.roles?.includes('super-admin') || user?.roles?.includes('admin') || false
    },
    create: ({ req: { user } }) => {
      return user?.roles?.includes('super-admin') || user?.roles?.includes('admin') || false
    },
    update: ({ req: { user } }) => {
      return user?.roles?.includes('super-admin') || user?.roles?.includes('admin') || false
    },
    delete: ({ req: { user } }) => {
      return user?.roles?.includes('super-admin') || user?.roles?.includes('admin') || false
    },
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      label: 'Rule Name',
      admin: {
        description: 'Internal name for this pricing rule',
      },
    },
    {
      name: 'site',
      type: 'relationship',
      relationTo: 'tenants',
      label: 'Site',
      admin: {
        description: 'Specific site (leave empty for global rule)',
      },
    },
    {
      name: 'placementType',
      type: 'select',
      required: true,
      options: [
        {
          label: 'Site-wide',
          value: 'site-wide',
        },
        {
          label: 'Homepage',
          value: 'homepage',
        },
        {
          label: 'Post Content',
          value: 'post-content',
        },
        {
          label: 'Page Content',
          value: 'page-content',
        },
        {
          label: 'Category Pages',
          value: 'category-pages',
        },
        {
          label: 'Tag Pages',
          value: 'tag-pages',
        },
        {
          label: 'Sidebar',
          value: 'sidebar',
        },
        {
          label: 'Header',
          value: 'header',
        },
        {
          label: 'Footer',
          value: 'footer',
        },
      ],
      admin: {
        description: 'Where the ad will be placed',
      },
    },
    {
      name: 'basePrice',
      type: 'number',
      required: true,
      label: 'Base Price per Day ($)',
      admin: {
        description: 'Base price per day in USD',
        step: 0.01,
      },
    },
    {
      name: 'multipliers',
      type: 'group',
      label: 'Price Multipliers',
      admin: {
        description: 'Factors that adjust the base price',
      },
      fields: [
        {
          name: 'weekendMultiplier',
          type: 'number',
          label: 'Weekend Multiplier',
          defaultValue: 1.0,
          admin: {
            description: 'Price multiplier for weekends (1.0 = no change, 1.5 = 50% more)',
            step: 0.1,
          },
        },
        {
          name: 'holidayMultiplier',
          type: 'number',
          label: 'Holiday Multiplier',
          defaultValue: 1.0,
          admin: {
            description: 'Price multiplier for holidays',
            step: 0.1,
          },
        },
        {
          name: 'peakSeasonMultiplier',
          type: 'number',
          label: 'Peak Season Multiplier',
          defaultValue: 1.0,
          admin: {
            description: 'Price multiplier for peak seasons',
            step: 0.1,
          },
        },
        {
          name: 'demandMultiplier',
          type: 'number',
          label: 'High Demand Multiplier',
          defaultValue: 1.0,
          admin: {
            description: 'Price multiplier when demand is high',
            step: 0.1,
          },
        },
      ],
    },
    {
      name: 'minimums',
      type: 'group',
      label: 'Minimum Requirements',
      fields: [
        {
          name: 'minimumDays',
          type: 'number',
          label: 'Minimum Days',
          defaultValue: 1,
          admin: {
            description: 'Minimum campaign duration in days',
          },
        },
        {
          name: 'minimumBudget',
          type: 'number',
          label: 'Minimum Budget ($)',
          defaultValue: 0,
          admin: {
            description: 'Minimum total budget required',
            step: 0.01,
          },
        },
      ],
    },
    {
      name: 'discounts',
      type: 'group',
      label: 'Volume Discounts',
      admin: {
        description: 'Discounts for longer campaigns',
      },
      fields: [
        {
          name: 'weeklyDiscount',
          type: 'number',
          label: 'Weekly Discount (%)',
          defaultValue: 0,
          admin: {
            description: 'Discount for campaigns 7+ days (percentage)',
            step: 0.1,
          },
        },
        {
          name: 'monthlyDiscount',
          type: 'number',
          label: 'Monthly Discount (%)',
          defaultValue: 0,
          admin: {
            description: 'Discount for campaigns 30+ days (percentage)',
            step: 0.1,
          },
        },
        {
          name: 'quarterlyDiscount',
          type: 'number',
          label: 'Quarterly Discount (%)',
          defaultValue: 0,
          admin: {
            description: 'Discount for campaigns 90+ days (percentage)',
            step: 0.1,
          },
        },
      ],
    },
    {
      name: 'isActive',
      type: 'checkbox',
      label: 'Active Rule',
      defaultValue: true,
      admin: {
        description: 'Whether this pricing rule is currently active',
      },
    },
    {
      name: 'priority',
      type: 'number',
      label: 'Priority',
      defaultValue: 100,
      admin: {
        description: 'Rule priority (lower numbers = higher priority)',
      },
    },
    {
      name: 'notes',
      type: 'textarea',
      label: 'Notes',
      admin: {
        description: 'Internal notes about this pricing rule',
      },
    },
  ],
  timestamps: true,
}
