import type { CollectionConfig } from 'payload'

export const AdCampaigns: CollectionConfig = {
  slug: 'ad-campaigns',
  labels: {
    singular: 'Ad Campaign',
    plural: 'Ad Campaigns',
  },
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'sponsor', 'status', 'startDate', 'endDate', 'totalBudget'],
    group: 'Sponsorship',
  },
  access: {
    // Sponsors can only see their own campaigns, admins can see all
    read: ({ req: { user } }) => {
      if (!user) return false
      if (user.roles?.includes('super-admin') || user.roles?.includes('admin')) {
        return true
      }
      if (user.roles?.includes('sponsor')) {
        return {
          sponsor: {
            equals: user.id,
          },
        }
      }
      return false
    },
    create: ({ req: { user } }) => {
      return user?.roles?.includes('sponsor') || user?.roles?.includes('super-admin') || user?.roles?.includes('admin') || false
    },
    update: ({ req: { user } }) => {
      if (user?.roles?.includes('super-admin') || user?.roles?.includes('admin')) {
        return true
      }
      if (user?.roles?.includes('sponsor')) {
        return {
          sponsor: {
            equals: user.id,
          },
        }
      }
      return false
    },
    delete: ({ req: { user } }) => {
      return user?.roles?.includes('super-admin') || user?.roles?.includes('admin') || false
    },
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      label: 'Campaign Name',
      admin: {
        description: 'Internal name for this advertising campaign',
      },
    },
    {
      name: 'sponsor',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      label: 'Sponsor',
      admin: {
        description: 'The sponsor who owns this campaign',
      },
      filterOptions: {
        roles: {
          contains: 'sponsor',
        },
      },
      hooks: {
        beforeChange: [
          ({ req, value }) => {
            // Auto-assign current user if they're a sponsor
            if (req.user?.roles?.includes('sponsor') && !value) {
              return req.user.id
            }
            return value
          },
        ],
      },
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      defaultValue: 'draft',
      options: [
        {
          label: 'Draft',
          value: 'draft',
        },
        {
          label: 'Pending Approval',
          value: 'pending',
        },
        {
          label: 'Approved',
          value: 'approved',
        },
        {
          label: 'Active',
          value: 'active',
        },
        {
          label: 'Paused',
          value: 'paused',
        },
        {
          label: 'Completed',
          value: 'completed',
        },
        {
          label: 'Rejected',
          value: 'rejected',
        },
      ],
      admin: {
        description: 'Current status of the campaign',
      },
      access: {
        update: ({ req: { user } }) => {
          // Sponsors can only change draft to pending, admins can change any status
          if (user?.roles?.includes('super-admin') || user?.roles?.includes('admin')) {
            return true
          }
          return false
        },
      },
    },
    {
      name: 'startDate',
      type: 'date',
      required: true,
      label: 'Start Date',
      admin: {
        description: 'When the campaign should start running',
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },
    {
      name: 'endDate',
      type: 'date',
      required: true,
      label: 'End Date',
      admin: {
        description: 'When the campaign should stop running',
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },
    {
      name: 'totalBudget',
      type: 'number',
      required: true,
      label: 'Total Budget ($)',
      admin: {
        description: 'Total budget for this campaign in USD',
        step: 0.01,
      },
    },
    {
      name: 'targeting',
      type: 'group',
      label: 'Targeting Options',
      fields: [
        {
          name: 'sites',
          type: 'relationship',
          relationTo: 'tenants',
          hasMany: true,
          label: 'Target Sites',
          admin: {
            description: 'Which sites should display this campaign',
          },
        },
        {
          name: 'targetingType',
          type: 'select',
          required: true,
          defaultValue: 'site-wide',
          options: [
            {
              label: 'Site-wide (all pages)',
              value: 'site-wide',
            },
            {
              label: 'Specific Posts',
              value: 'posts',
            },
            {
              label: 'Specific Pages',
              value: 'pages',
            },
            {
              label: 'Categories',
              value: 'categories',
            },
            {
              label: 'Tags',
              value: 'tags',
            },
          ],
          admin: {
            description: 'How to target content on the selected sites',
          },
        },
        {
          name: 'specificPosts',
          type: 'relationship',
          relationTo: 'posts',
          hasMany: true,
          label: 'Specific Posts',
          admin: {
            condition: (data) => data?.targeting?.targetingType === 'posts',
            description: 'Specific posts to target',
          },
        },
        {
          name: 'specificPages',
          type: 'relationship',
          relationTo: 'pages',
          hasMany: true,
          label: 'Specific Pages',
          admin: {
            condition: (data) => data?.targeting?.targetingType === 'pages',
            description: 'Specific pages to target',
          },
        },
        {
          name: 'categories',
          type: 'relationship',
          relationTo: 'categories',
          hasMany: true,
          label: 'Categories',
          admin: {
            condition: (data) => data?.targeting?.targetingType === 'categories',
            description: 'Target posts in these categories',
          },
        },
        {
          name: 'tags',
          type: 'relationship',
          relationTo: 'tags',
          hasMany: true,
          label: 'Tags',
          admin: {
            condition: (data) => data?.targeting?.targetingType === 'tags',
            description: 'Target posts with these tags',
          },
        },
      ],
    },
  ],
  timestamps: true,
}
