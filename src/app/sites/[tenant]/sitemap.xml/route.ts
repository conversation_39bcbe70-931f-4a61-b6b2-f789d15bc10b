import { NextRequest, NextResponse } from 'next/server'
import { getTenantBySlug } from '@/lib/tenant'
import { getPayload } from 'payload'
import config from '@payload-config'

// Force dynamic rendering for multi-tenant
export const dynamic = 'force-dynamic'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ tenant: string }> },
) {
  const { tenant: tenantSlug } = await params

  // Get tenant information
  const tenant = await getTenantBySlug(tenantSlug)
  if (!tenant) {
    return new NextResponse('Tenant not found', { status: 404 })
  }

  const baseUrl = `https://${tenant.domain}`
  const payload = await getPayload({ config })

  try {
    // Get all published posts
    const posts = await payload.find({
      collection: 'posts',
      where: {
        and: [
          {
            tenant: {
              equals: tenant.id,
            },
          },
          {
            status: {
              equals: 'published',
            },
          },
        ],
      },
      limit: 1000,
      sort: '-publishedAt',
    })

    // Get all published pages
    const pages = await payload.find({
      collection: 'pages',
      where: {
        and: [
          {
            tenant: {
              equals: tenant.id,
            },
          },
          {
            status: {
              equals: 'published',
            },
          },
        ],
      },
      limit: 1000,
    })

    // Get all categories
    const categories = await payload.find({
      collection: 'categories',
      where: {
        tenant: {
          equals: tenant.id,
        },
      },
      limit: 1000,
    })

    // Get all authors
    const authors = await payload.find({
      collection: 'authors',
      where: {
        tenant: {
          equals: tenant.id,
        },
      },
      limit: 1000,
    })

    // Get all tags
    const tags = await payload.find({
      collection: 'tags',
      where: {
        tenant: {
          equals: tenant.id,
        },
      },
      limit: 1000,
    })

    // Build sitemap entries
    let sitemapEntries = ''

    // Homepage
    sitemapEntries += `
  <url>
    <loc>${baseUrl}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>`

    // Posts
    posts.docs.forEach((post: any) => {
      sitemapEntries += `
  <url>
    <loc>${baseUrl}/${post.slug}</loc>
    <lastmod>${new Date(post.updatedAt).toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>`
    })

    // Pages
    pages.docs.forEach((page: any) => {
      sitemapEntries += `
  <url>
    <loc>${baseUrl}/${page.slug}</loc>
    <lastmod>${new Date(page.updatedAt).toISOString()}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.7</priority>
  </url>`
    })

    // Categories
    categories.docs.forEach((category: any) => {
      sitemapEntries += `
  <url>
    <loc>${baseUrl}/category/${category.slug}</loc>
    <lastmod>${new Date(category.updatedAt).toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.6</priority>
  </url>`
    })

    // Authors
    authors.docs.forEach((author: any) => {
      sitemapEntries += `
  <url>
    <loc>${baseUrl}/author/${author.slug}</loc>
    <lastmod>${new Date(author.updatedAt).toISOString()}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.5</priority>
  </url>`
    })

    // Tags
    tags.docs.forEach((tag: any) => {
      sitemapEntries += `
  <url>
    <loc>${baseUrl}/tag/${tag.slug}</loc>
    <lastmod>${new Date(tag.updatedAt).toISOString()}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.4</priority>
  </url>`
    })

    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">${sitemapEntries}
</urlset>`

    return new NextResponse(sitemap, {
      headers: {
        'Content-Type': 'application/xml; charset=utf-8',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600',
      },
    })
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('Error generating sitemap:', error)
    }

    // Return basic sitemap on error
    const basicSitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${baseUrl}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
</urlset>`

    return new NextResponse(basicSitemap, {
      headers: {
        'Content-Type': 'application/xml; charset=utf-8',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600',
      },
    })
  }
}
