import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'
import { S3Client, GetObjectCommand } from '@aws-sdk/client-s3'
import { slugifyFilename, deSlugifyFilename } from '../../../../../utils/slugify'

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ tenant: string; filename: string }> },
) {
  const { tenant, filename } = await params

  try {
    const payload = await getPayload({ config })

    // Find the media file in the database by filename and tenant prefix
    // Handle both original filenames and slugified filenames
    let media = await payload.find({
      collection: 'media',
      where: {
        and: [
          {
            filename: {
              equals: filename,
            },
          },
          {
            prefix: {
              equals: tenant,
            },
          },
        ],
      },
      limit: 1,
    })

    // If no exact match found, try to find by slugified filename matching
    if (!media.docs.length) {
      // Get all media files for this tenant and find the best match
      const allMedia = await payload.find({
        collection: 'media',
        where: {
          prefix: {
            equals: tenant,
          },
        },
        limit: 100, // Get more results to find the best match
      })

      console.log('🔍 Searching for filename match:', {
        requestedFilename: filename,
        tenant,
        availableFiles: allMedia.docs.map((doc) => ({
          id: doc.id,
          filename: doc.filename,
          slugified: doc.filename ? slugifyFilename(doc.filename) : null,
        })),
      })

      // Find the best match by comparing slugified versions
      const targetSlugified = filename.toLowerCase()
      for (const doc of allMedia.docs) {
        if (!doc.filename) continue // Skip if filename is null/undefined

        const docSlugified = slugifyFilename(doc.filename)

        if (docSlugified === targetSlugified) {
          media = {
            docs: [doc],
            totalDocs: 1,
            limit: 1,
            totalPages: 1,
            page: 1,
            pagingCounter: 1,
            hasPrevPage: false,
            hasNextPage: false,
            prevPage: null,
            nextPage: null,
          }
          console.log('✅ Found exact match:', { filename: doc.filename, slugified: docSlugified })
          break
        }
      }

      // If still no match, try to match by base filename (for resized images)
      if (!media.docs.length) {
        const baseFilename = filename.replace(/-\d+x\d+\.(webp|jpg|jpeg|png)$/i, '')
        const targetBaseSlugified = baseFilename.toLowerCase()

        for (const doc of allMedia.docs) {
          if (!doc.filename) continue

          const docSlugified = slugifyFilename(doc.filename)
          const docBaseSlugified = docSlugified.replace(/\.[^/.]+$/, '') // Remove extension

          if (docBaseSlugified === targetBaseSlugified) {
            media = {
              docs: [doc],
              totalDocs: 1,
              limit: 1,
              totalPages: 1,
              page: 1,
              pagingCounter: 1,
              hasPrevPage: false,
              hasNextPage: false,
              prevPage: null,
              nextPage: null,
            }
            console.log('✅ Found base filename match:', {
              filename: doc.filename,
              baseMatch: true,
            })
            break
          }
        }
      }
    }

    let mediaDoc = null

    if (media.docs.length > 0) {
      mediaDoc = media.docs[0]
    } else {
      console.log('❌ No media file found for:', { tenant, filename })
      return new NextResponse('Media not found', { status: 404 })
    }

    // Fetch from Cloudflare R2 using AWS SDK
    if (process.env.S3_BUCKET && process.env.S3_ACCESS_KEY_ID) {
      const s3Client = new S3Client({
        region: process.env.S3_REGION || 'auto',
        endpoint: process.env.S3_ENDPOINT,
        credentials: {
          accessKeyId: process.env.S3_ACCESS_KEY_ID,
          secretAccessKey: process.env.S3_SECRET_ACCESS_KEY || '',
        },
        forcePathStyle: true, // Required for Cloudflare R2
      })

      try {
        // Construct the S3 key using tenant prefix + filename
        // For resized images, we need to use the resized filename, not the original
        if (!mediaDoc.filename) {
          return new NextResponse('Media filename not found', { status: 404 })
        }

        let s3Key = `${tenant}/${mediaDoc.filename}` // Default to original file

        // Check if this is a request for a resized image
        if (
          filename.includes('-300x300.webp') ||
          filename.includes('-640x480.webp') ||
          filename.includes('-1200x630.webp')
        ) {
          // This is a resized image - try to find it with the original filename format
          const sizeMatch = filename.match(/-(\d+x\d+)\.webp$/)
          if (sizeMatch) {
            const size = sizeMatch[1]
            const originalName = mediaDoc.filename
            const nameWithoutExt = originalName.replace(/\.[^/.]+$/, '') // Remove extension
            const resizedFilename = `${nameWithoutExt}-${size}.webp`
            s3Key = `${tenant}/${resizedFilename}`
          }
        }

        console.log('🔍 Fetching from R2:', {
          tenant,
          requestedFilename: filename,
          originalFilename: mediaDoc.filename,
          s3Key,
        })

        let command = new GetObjectCommand({
          Bucket: process.env.S3_BUCKET,
          Key: s3Key,
        })

        let response
        try {
          response = await s3Client.send(command)
        } catch (error: any) {
          // If resized image not found, try the original file as fallback
          if (error.name === 'NoSuchKey' && s3Key !== `${tenant}/${mediaDoc.filename}`) {
            console.log('🔄 Resized image not found, trying original file')
            const fallbackKey = `${tenant}/${mediaDoc.filename}`
            command = new GetObjectCommand({
              Bucket: process.env.S3_BUCKET,
              Key: fallbackKey,
            })
            response = await s3Client.send(command)
          } else {
            throw error
          }
        }

        if (response.Body) {
          // Convert the stream to buffer
          const uint8Array = await response.Body.transformToByteArray()
          const buffer = Buffer.from(uint8Array)

          // Determine content type from filename extension
          const ext = filename.split('.').pop()?.toLowerCase()
          let contentType = 'application/octet-stream'

          switch (ext) {
            case 'jpg':
            case 'jpeg':
              contentType = 'image/jpeg'
              break
            case 'png':
              contentType = 'image/png'
              break
            case 'gif':
              contentType = 'image/gif'
              break
            case 'webp':
              contentType = 'image/webp'
              break
            case 'svg':
              contentType = 'image/svg+xml'
              break
            case 'pdf':
              contentType = 'application/pdf'
              break
            case 'mp4':
              contentType = 'video/mp4'
              break
            case 'webm':
              contentType = 'video/webm'
              break
          }

          return new NextResponse(buffer, {
            headers: {
              'Content-Type': contentType,
              'Content-Length': buffer.length.toString(),
              'Cache-Control': 'public, max-age=31536000, immutable',
              ETag: response.ETag || `"${mediaDoc?.id || 'unknown'}"`,
              'Last-Modified': response.LastModified?.toUTCString() || new Date().toUTCString(),
            },
          })
        }
      } catch (s3Error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Error fetching from R2:', s3Error)
        }
        return new NextResponse('Media file not found in storage', { status: 404 })
      }
    }

    return new NextResponse('Storage not configured', { status: 500 })
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('Error serving media:', error)
    }
    return new NextResponse('Internal server error', { status: 500 })
  }
}
