import { NextRequest, NextResponse } from 'next/server'
import { getTenantBySlug } from '@/lib/tenant'
import { getPayload } from 'payload'
import config from '@payload-config'

// Force dynamic rendering for multi-tenant
export const dynamic = 'force-dynamic'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ tenant: string }> },
) {
  const { tenant: tenantSlug } = await params

  // Get tenant information
  const tenant = await getTenantBySlug(tenantSlug)
  if (!tenant) {
    return new NextResponse('Tenant not found', { status: 404 })
  }

  const baseUrl = `https://${tenant.domain}`
  const payload = await getPayload({ config })

  try {
    // Get latest 20 published posts
    const posts = await payload.find({
      collection: 'posts',
      where: {
        and: [
          {
            tenant: {
              equals: tenant.id,
            },
          },
          {
            status: {
              equals: 'published',
            },
          },
        ],
      },
      limit: 20,
      sort: '-publishedAt',
      depth: 2,
    })

    const rssItems = posts.docs
      .map((post: any) => {
        const pubDate = new Date(post.publishedAt || post.createdAt).toUTCString()
        const author = post.author?.name || tenant.name
        const categories = post.categories?.map((cat: any) => cat.name).join(', ') || ''

        return `
    <item>
      <title><![CDATA[${post.title}]]></title>
      <link>${baseUrl}/${post.slug}</link>
      <guid isPermaLink="true">${baseUrl}/${post.slug}</guid>
      <description><![CDATA[${post.excerpt || ''}]]></description>
      <pubDate>${pubDate}</pubDate>
      <author>${post.author?.email || 'noreply@' + tenant.domain} (${author})</author>
      ${categories ? `<category><![CDATA[${categories}]]></category>` : ''}
      ${post.featuredImage ? `<enclosure url="${post.featuredImage.url}" type="${post.featuredImage.mimeType}" length="${post.featuredImage.filesize}" />` : ''}
    </item>`
      })
      .join('')

    const rss = `<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0" xmlns:content="http://purl.org/rss/1.0/modules/content/" xmlns:wfw="http://wellformedweb.org/CommentAPI/" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:atom="http://www.w3.org/2005/Atom" xmlns:sy="http://purl.org/rss/1.0/modules/syndication/" xmlns:slash="http://purl.org/rss/1.0/modules/slash/">
  <channel>
    <title><![CDATA[${tenant.name}]]></title>
    <atom:link href="${baseUrl}/feed.xml" rel="self" type="application/rss+xml" />
    <link>${baseUrl}</link>
    <description><![CDATA[${tenant.seoDefaults?.description || `Latest posts from ${tenant.name}`}]]></description>
    <lastBuildDate>${new Date().toUTCString()}</lastBuildDate>
    <language>en-US</language>
    <sy:updatePeriod>hourly</sy:updatePeriod>
    <sy:updateFrequency>1</sy:updateFrequency>
    <generator>CraftPress Multi-Tenant CMS</generator>
    <image>
      <url>${tenant.logo?.url || `${baseUrl}/favicon.ico`}</url>
      <title><![CDATA[${tenant.name}]]></title>
      <link>${baseUrl}</link>
    </image>${rssItems}
  </channel>
</rss>`

    return new NextResponse(rss, {
      headers: {
        'Content-Type': 'application/rss+xml; charset=utf-8',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600',
      },
    })
  } catch (error) {
    console.error('Error generating RSS feed:', error)
    return new NextResponse('Error generating RSS feed', { status: 500 })
  }
}
