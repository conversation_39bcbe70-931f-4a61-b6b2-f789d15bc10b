import { notFound } from 'next/navigation'
import type { Metadata } from 'next'
import { getTenantBySlug } from '@/lib/tenant'

// Force dynamic rendering for multi-tenant
export const dynamic = 'force-dynamic'
import {
  getContentBySlug,
  getCategoryBySlug,
  getTagBySlug,
  getAuthorBySlug,
  getPostsByCategory,
  getPostsByTag,
  getPostsByAuthor,
} from '@/lib/content'
import StructuredData from '@/components/StructuredData'

interface PageProps {
  params: Promise<{
    tenant: string
    slug?: string[]
  }>
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { tenant: tenantSlug, slug } = await params
  const tenant = await getTenantBySlug(tenantSlug)

  if (!tenant) {
    return {
      title: 'Not Found',
      description: 'The requested page could not be found.',
    }
  }

  const baseUrl = `https://${tenant.domain}`

  // Homepage
  if (!slug || slug.length === 0) {
    const ogImageUrl = `${baseUrl}/og`

    return {
      title: tenant.name,
      description: tenant.seoDefaults?.description || `Welcome to ${tenant.name}`,
      openGraph: {
        title: tenant.name,
        description: tenant.seoDefaults?.description || `Welcome to ${tenant.name}`,
        url: baseUrl,
        siteName: tenant.name,
        images: [
          {
            url: ogImageUrl,
            width: 1200,
            height: 630,
            alt: tenant.name,
          },
        ],
        locale: 'en_US',
        type: 'website',
      },
      twitter: {
        card: 'summary_large_image',
        title: tenant.name,
        description: tenant.seoDefaults?.description || `Welcome to ${tenant.name}`,
        images: [ogImageUrl],
      },
      alternates: {
        canonical: baseUrl,
        types: {
          'application/rss+xml': `${baseUrl}/feed.xml`,
        },
      },
    }
  }

  const [firstSlug, secondSlug] = slug

  // Category, tag, or author pages
  if (firstSlug && secondSlug) {
    const itemSlug = secondSlug

    switch (firstSlug) {
      case 'category':
        const category = await getCategoryBySlug(itemSlug, tenant)
        if (category) {
          const categoryUrl = `${baseUrl}/category/${category.slug}`

          return {
            title: `${category.name} | ${tenant.name}`,
            description: category.description || `Posts in ${category.name} category`,
            alternates: {
              canonical: categoryUrl,
            },
            openGraph: {
              title: `${category.name} | ${tenant.name}`,
              description: category.description || `Posts in ${category.name} category`,
              url: categoryUrl,
              siteName: tenant.name,
              type: 'website',
            },
          }
        }
        break

      case 'author':
        const author = await getAuthorBySlug(itemSlug, tenant)
        if (author) {
          const authorUrl = `${baseUrl}/author/${author.slug}`

          return {
            title: `${author.name} | ${tenant.name}`,
            description: author.bio || `Posts by ${author.name}`,
            alternates: {
              canonical: authorUrl,
            },
            openGraph: {
              title: `${author.name} | ${tenant.name}`,
              description: author.bio || `Posts by ${author.name}`,
              url: authorUrl,
              siteName: tenant.name,
              type: 'profile',
            },
          }
        }
        break

      case 'tag':
        const tag = await getTagBySlug(itemSlug, tenant)
        if (tag) {
          const tagUrl = `${baseUrl}/tag/${tag.slug}`

          return {
            title: `${tag.name} | ${tenant.name}`,
            description: tag.description || `Posts tagged with ${tag.name}`,
            alternates: {
              canonical: tagUrl,
            },
            openGraph: {
              title: `${tag.name} | ${tenant.name}`,
              description: tag.description || `Posts tagged with ${tag.name}`,
              url: tagUrl,
              siteName: tenant.name,
              type: 'website',
            },
          }
        }
        break
    }
  }

  // Individual post or page
  if (firstSlug) {
    const content = await getContentBySlug(firstSlug, tenant)
    if (content) {
      const isPost = content.publishedAt !== undefined
      const ogImageUrl = content.featuredImage?.url || `${baseUrl}/og?slug=${content.slug}`

      // Generate comprehensive robots meta tag with advanced directives
      const robots = []
      const meta = (content as any).meta || {}

      // Basic indexing directives
      if (meta.noIndex) {
        robots.push('noindex')
      } else {
        robots.push('index')
      }

      if (meta.noFollow) {
        robots.push('nofollow')
      } else {
        robots.push('follow')
      }

      // Advanced robots directives
      if (meta.noArchive) robots.push('noarchive')
      if (meta.noSnippet) robots.push('nosnippet')
      if (meta.noImageIndex) robots.push('noimageindex')

      // Max snippet length
      if (meta.maxSnippet !== undefined && meta.maxSnippet !== -1) {
        robots.push(`max-snippet:${meta.maxSnippet}`)
      }

      // Max image preview
      if (meta.maxImagePreview && meta.maxImagePreview !== 'large') {
        robots.push(`max-image-preview:${meta.maxImagePreview}`)
      }

      // Max video preview
      if (meta.maxVideoPreview !== undefined && meta.maxVideoPreview !== -1) {
        robots.push(`max-video-preview:${meta.maxVideoPreview}`)
      }

      // Generate canonical URL with proper validation and multi-tenant awareness
      let canonicalUrl = meta.canonicalUrl

      if (!canonicalUrl) {
        // Generate default canonical URL for this tenant
        canonicalUrl = `${baseUrl}/${content.slug}`
      } else {
        // Validate custom canonical URL
        try {
          new URL(canonicalUrl)
        } catch {
          // Fallback to default if custom URL is invalid
          canonicalUrl = `${baseUrl}/${content.slug}`
        }
      }

      return {
        title: (content as any).meta?.title || `${content.title} | ${tenant.name}`,
        description:
          (content as any).meta?.description ||
          content.excerpt ||
          (content as any).seo?.description || // Fallback for existing data
          `Read ${content.title} on ${tenant.name}`,
        robots: robots.join(', '),

        // Use validated canonical URL
        alternates: {
          canonical: canonicalUrl,
        },

        authors: isPost && content.author ? [{ name: content.author.name }] : undefined,
        openGraph: {
          title: (content as any).meta?.title || content.title,
          description:
            (content as any).meta?.description ||
            content.excerpt ||
            (content as any).seo?.description || // Fallback for existing data
            `Read ${content.title} on ${tenant.name}`,
          url: canonicalUrl, // Ensure OpenGraph URL matches canonical URL
          siteName: tenant.name,
          images: [
            {
              url: (content as any).meta?.image?.url || ogImageUrl,
              width: 1200,
              height: 630,
              alt: (content as any).meta?.title || content.title,
            },
          ],
          locale: 'en_US',
          type: isPost ? 'article' : 'website',
          ...(isPost && {
            publishedTime: content.publishedAt,
            modifiedTime: content.updatedAt,
            authors: content.author ? [content.author.name] : [],
            tags: content.tags?.map((tag: any) => tag.name) || [],
          }),
        },
        twitter: {
          card: 'summary_large_image',
          title: (content as any).meta?.title || content.title,
          description:
            (content as any).meta?.description ||
            content.excerpt ||
            (content as any).seo?.description || // Fallback for existing data
            `Read ${content.title} on ${tenant.name}`,
          images: [(content as any).meta?.image?.url || ogImageUrl],
        },
      }
    }
  }

  return {
    title: 'Not Found',
    description: 'The requested page could not be found.',
  }
}

// Enable ISR with 1 hour revalidation
export const revalidate = 3600

export default async function TenantPage({ params }: PageProps) {
  // Await params for Next.js 15 compatibility
  const resolvedParams = await params

  // Get tenant information by slug from URL
  const tenant = await getTenantBySlug(resolvedParams.tenant)
  if (!tenant) {
    notFound()
  }

  const slug = resolvedParams.slug || []
  const path = slug.join('/')

  // Handle different URL patterns
  if (slug.length === 0) {
    // Homepage
    return <HomePage tenant={tenant} />
  }

  if (slug.length === 1) {
    const [firstSegment] = slug

    // Check if it's a page first
    const page = await getContentBySlug(firstSegment, tenant, 'pages')
    if (page) {
      return <PageView content={page} tenant={tenant} />
    }

    // Check if it's a post
    const post = await getContentBySlug(firstSegment, tenant, 'posts')
    if (post) {
      return <PostView content={post} tenant={tenant} />
    }
  }

  if (slug.length === 2) {
    const [type, itemSlug] = slug

    switch (type) {
      case 'category':
        const category = await getCategoryBySlug(itemSlug, tenant)
        if (category) {
          const posts = await getPostsByCategory(category.id, tenant, 10, 1)
          return <CategoryView category={category} posts={posts} tenant={tenant} />
        }
        break

      case 'tag':
        const tag = await getTagBySlug(itemSlug, tenant)
        if (tag) {
          const posts = await getPostsByTag(tag.id, tenant, 10, 1)
          return <TagView tag={tag} posts={posts} tenant={tenant} />
        }
        break

      case 'author':
        const author = await getAuthorBySlug(itemSlug, tenant)
        if (author) {
          const posts = await getPostsByAuthor(author.id, tenant, 10, 1)
          return <AuthorView author={author} posts={posts} tenant={tenant} />
        }
        break
    }
  }

  // If nothing matches, return 404
  notFound()
}

// Homepage component
function HomePage({ tenant }: { tenant: any }) {
  return (
    <div className="min-h-screen bg-white">
      <StructuredData tenant={tenant} type="website" />
      <StructuredData tenant={tenant} type="organization" />

      <header className="bg-blue-600 text-white py-8">
        <div className="container mx-auto px-4">
          <h1 className="text-4xl font-bold">{tenant.name}</h1>
          <p className="text-xl mt-2">
            {tenant.seoDefaults?.description || 'Welcome to our website'}
          </p>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h2 className="text-2xl font-semibold mb-4">Welcome to {tenant.name}</h2>
          <p className="text-gray-600 mb-8">This is the homepage for {tenant.slug} tenant.</p>

          <div className="bg-gray-100 p-6 rounded-lg">
            <h3 className="text-lg font-medium mb-2">Tenant Information</h3>
            <div className="text-left space-y-2">
              <p>
                <strong>Name:</strong> {tenant.name}
              </p>
              <p>
                <strong>Slug:</strong> {tenant.slug}
              </p>
              <p>
                <strong>Domain:</strong> {tenant.domain}
              </p>
              <p>
                <strong>Status:</strong> {tenant.isActive ? 'Active' : 'Inactive'}
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}

// Page view component
function PageView({ content, tenant }: { content: any; tenant: any }) {
  return (
    <div className="min-h-screen bg-white">
      <header className="bg-green-600 text-white py-8">
        <div className="container mx-auto px-4">
          <h1 className="text-4xl font-bold">{content.title}</h1>
          <p className="text-sm mt-2">Page on {tenant.name}</p>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <article className="prose max-w-none">
          {content.excerpt && <p className="text-xl text-gray-600 mb-6">{content.excerpt}</p>}

          <div className="bg-gray-50 p-6 rounded-lg">
            <h3 className="text-lg font-medium mb-2">Page Content</h3>
            <p>Content would be rendered here from the rich text editor.</p>
            <p className="text-sm text-gray-500 mt-4">
              Published:{' '}
              {content.publishedAt ? new Date(content.publishedAt).toLocaleDateString() : 'Draft'}
            </p>
          </div>
        </article>
      </main>
    </div>
  )
}

// Post view component
function PostView({ content, tenant }: { content: any; tenant: any }) {
  const breadcrumbs = [
    { name: 'Home', url: `https://${tenant.domain}` },
    { name: content.title, url: `https://${tenant.domain}/${content.slug}` },
  ]

  return (
    <div className="min-h-screen bg-white">
      <StructuredData tenant={tenant} content={content} type="article" />
      <StructuredData tenant={tenant} type="breadcrumb" breadcrumbs={breadcrumbs} />

      <header className="bg-purple-600 text-white py-8">
        <div className="container mx-auto px-4">
          <h1 className="text-4xl font-bold">{content.title}</h1>
          <p className="text-sm mt-2">Post on {tenant.name}</p>
          {content.author && (
            <p className="text-sm">By {content.author.name || content.author.email}</p>
          )}
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <article className="prose max-w-none">
          {content.excerpt && <p className="text-xl text-gray-600 mb-6">{content.excerpt}</p>}

          <div className="bg-gray-50 p-6 rounded-lg mb-6">
            <h3 className="text-lg font-medium mb-2">Post Content</h3>
            <p>Content would be rendered here from the rich text editor.</p>
          </div>

          {(content.categories?.length > 0 || content.tags?.length > 0) && (
            <div className="border-t pt-6">
              {content.categories?.length > 0 && (
                <div className="mb-4">
                  <h4 className="font-medium mb-2">Categories:</h4>
                  <div className="flex flex-wrap gap-2">
                    {content.categories.map((cat: any) => (
                      <span
                        key={cat.id}
                        className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm"
                      >
                        {cat.name}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {content.tags?.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">Tags:</h4>
                  <div className="flex flex-wrap gap-2">
                    {content.tags.map((tag: any) => (
                      <span
                        key={tag.id}
                        className="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm"
                      >
                        {tag.name}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          <p className="text-sm text-gray-500 mt-6">
            Published:{' '}
            {content.publishedAt ? new Date(content.publishedAt).toLocaleDateString() : 'Draft'}
          </p>
        </article>
      </main>
    </div>
  )
}

// Category view component
function CategoryView({ category, posts, tenant }: { category: any; posts: any; tenant: any }) {
  return (
    <div className="min-h-screen bg-white">
      <header className="bg-orange-600 text-white py-8">
        <div className="container mx-auto px-4">
          <h1 className="text-4xl font-bold">Category: {category.name}</h1>
          <p className="text-sm mt-2">On {tenant.name}</p>
          {category.description && <p className="mt-2">{category.description}</p>}
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <h2 className="text-2xl font-semibold mb-4">
            Posts in {category.name} ({posts.totalDocs})
          </h2>
        </div>

        {posts.docs.length > 0 ? (
          <div className="grid gap-6">
            {posts.docs.map((post: any) => (
              <article key={post.id} className="border rounded-lg p-6">
                <h3 className="text-xl font-semibold mb-2">{post.title}</h3>
                {post.excerpt && <p className="text-gray-600 mb-4">{post.excerpt}</p>}
                <div className="text-sm text-gray-500">
                  <p>
                    Published:{' '}
                    {post.publishedAt ? new Date(post.publishedAt).toLocaleDateString() : 'Draft'}
                  </p>
                  {post.author && <p>By {post.author.name || post.author.email}</p>}
                </div>
              </article>
            ))}
          </div>
        ) : (
          <p className="text-gray-600">No posts found in this category.</p>
        )}
      </main>
    </div>
  )
}

// Tag view component
function TagView({ tag, posts, tenant }: { tag: any; posts: any; tenant: any }) {
  return (
    <div className="min-h-screen bg-white">
      <header className="bg-red-600 text-white py-8">
        <div className="container mx-auto px-4">
          <h1 className="text-4xl font-bold">Tag: {tag.name}</h1>
          <p className="text-sm mt-2">On {tenant.name}</p>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <h2 className="text-2xl font-semibold mb-4">
            Posts tagged with {tag.name} ({posts.totalDocs})
          </h2>
        </div>

        {posts.docs.length > 0 ? (
          <div className="grid gap-6">
            {posts.docs.map((post: any) => (
              <article key={post.id} className="border rounded-lg p-6">
                <h3 className="text-xl font-semibold mb-2">{post.title}</h3>
                {post.excerpt && <p className="text-gray-600 mb-4">{post.excerpt}</p>}
                <div className="text-sm text-gray-500">
                  <p>
                    Published:{' '}
                    {post.publishedAt ? new Date(post.publishedAt).toLocaleDateString() : 'Draft'}
                  </p>
                  {post.author && <p>By {post.author.name || post.author.email}</p>}
                </div>
              </article>
            ))}
          </div>
        ) : (
          <p className="text-gray-600">No posts found with this tag.</p>
        )}
      </main>
    </div>
  )
}

// Author view component
function AuthorView({ author, posts, tenant }: { author: any; posts: any; tenant: any }) {
  return (
    <div className="min-h-screen bg-white">
      <header className="bg-indigo-600 text-white py-8">
        <div className="container mx-auto px-4">
          <h1 className="text-4xl font-bold">Author: {author.name}</h1>
          <p className="text-sm mt-2">On {tenant.name}</p>
          {author.bio && <p className="text-lg mt-4">{author.bio}</p>}
          {author.email && <p className="text-sm mt-2">Contact: {author.email}</p>}
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <h2 className="text-2xl font-semibold mb-6">
          Posts by {author.name} ({posts.totalDocs})
        </h2>

        {posts.docs.length > 0 ? (
          <div className="space-y-6">
            {posts.docs.map((post: any) => (
              <article key={post.id} className="border-b border-gray-200 pb-6">
                <h3 className="text-xl font-semibold mb-2">
                  <a href={`/${post.slug}`} className="text-indigo-600 hover:text-indigo-800">
                    {post.title}
                  </a>
                </h3>
                {post.excerpt && <p className="text-gray-600 mb-2">{post.excerpt}</p>}
                <div className="text-sm text-gray-500">
                  <p>
                    Published:{' '}
                    {post.publishedAt ? new Date(post.publishedAt).toLocaleDateString() : 'Draft'}
                  </p>
                </div>
              </article>
            ))}
          </div>
        ) : (
          <p className="text-gray-600">No posts found by this author.</p>
        )}
      </main>
    </div>
  )
}
