import { ImageResponse } from 'next/og'
import { NextRequest } from 'next/server'
import { getTenantBySlug } from '@/lib/tenant'
import { getContentBySlug } from '@/lib/content'

// Force dynamic rendering for multi-tenant
export const dynamic = 'force-dynamic'

// Using Node.js runtime for full PayloadCMS compatibility

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ tenant: string }> },
) {
  const { tenant: tenantSlug } = await params
  const { searchParams } = new URL(request.url)
  const slug = searchParams.get('slug')
  const type = searchParams.get('type') || 'default'

  // Get tenant information
  const tenant = await getTenantBySlug(tenantSlug)
  if (!tenant) {
    return new Response('Tenant not found', { status: 404 })
  }

  try {
    let title = tenant.name
    let description = tenant.seoDefaults?.description || `Welcome to ${tenant.name}`
    let category = ''

    // If slug is provided, get content-specific information
    if (slug) {
      const content = await getContentBySlug(slug, tenant)
      if (content) {
        title = content.title
        description = content.excerpt || content.seo?.description || description
        if (content.category) {
          category = typeof content.category === 'object' ? content.category.name : content.category
        }
      }
    }

    // Determine theme colors based on tenant
    const getThemeColors = () => {
      switch (tenant.slug) {
        case 'timeeting':
          return {
            primary: '#10B981', // emerald-500 (timeeting brand color)
            secondary: '#059669', // emerald-600
            accent: '#c084fc', // purple-400
          }
        case 'timeeting':
          return {
            primary: '#2563eb', // blue-600
            secondary: '#3b82f6', // blue-500
            accent: '#60a5fa', // blue-400
          }
        default:
          return {
            primary: '#1f2937', // gray-800
            secondary: '#374151', // gray-700
            accent: '#6b7280', // gray-500
          }
      }
    }

    const colors = getThemeColors()

    return new ImageResponse(
      (
        <div
          style={{
            height: '100%',
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'white',
            backgroundImage: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.secondary} 100%)`,
          }}
        >
          {/* Main content container */}
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
              borderRadius: '24px',
              padding: '60px',
              margin: '40px',
              boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
              width: '90%',
              height: '80%',
            }}
          >
            {/* Site name */}
            <div
              style={{
                fontSize: '32px',
                fontWeight: '600',
                color: colors.primary,
                marginBottom: '20px',
                textAlign: 'center',
              }}
            >
              {tenant.name}
            </div>

            {/* Category badge */}
            {category && (
              <div
                style={{
                  backgroundColor: colors.accent,
                  color: 'white',
                  padding: '8px 16px',
                  borderRadius: '20px',
                  fontSize: '16px',
                  fontWeight: '500',
                  marginBottom: '30px',
                }}
              >
                {category}
              </div>
            )}

            {/* Main title */}
            <div
              style={{
                fontSize: slug ? '48px' : '56px',
                fontWeight: '800',
                color: '#1f2937',
                textAlign: 'center',
                lineHeight: '1.1',
                marginBottom: '30px',
                maxWidth: '90%',
              }}
            >
              {title}
            </div>

            {/* Description */}
            <div
              style={{
                fontSize: '24px',
                color: '#6b7280',
                textAlign: 'center',
                lineHeight: '1.4',
                maxWidth: '80%',
              }}
            >
              {description.length > 120 ? description.substring(0, 120) + '...' : description}
            </div>
          </div>

          {/* Bottom branding */}
          <div
            style={{
              position: 'absolute',
              bottom: '40px',
              right: '40px',
              display: 'flex',
              alignItems: 'center',
              color: 'white',
              fontSize: '18px',
              fontWeight: '500',
            }}
          >
            <div
              style={{
                width: '12px',
                height: '12px',
                backgroundColor: 'white',
                borderRadius: '50%',
                marginRight: '8px',
              }}
            />
            {tenant.domain}
          </div>
        </div>
      ),
      {
        width: 1200,
        height: 630,
      },
    )
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('Error generating OG image:', error)
    }
    return new Response('Error generating image', { status: 500 })
  }
}
