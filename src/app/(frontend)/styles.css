@import 'tailwindcss';

/* Import Inter font */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* CSS Custom Properties for site-specific theming */
:root {
  /* Default brand colors (blue) - will be overridden per site */
  --brand-50: 239 246 255;
  --brand-100: 219 234 254;
  --brand-200: 191 219 254;
  --brand-300: 147 197 253;
  --brand-400: 96 165 250;
  --brand-500: 59 130 246;
  --brand-600: 37 99 235;
  --brand-700: 29 78 216;
  --brand-800: 30 64 175;
  --brand-900: 30 58 138;
  --brand-950: 23 37 84;

  /* Light mode colors */
  --background: 255 255 255;
  --foreground: 23 23 23;
  --muted: 245 245 245;
  --muted-foreground: 115 115 115;
  --border: 229 229 229;
  --input: 229 229 229;
  --card: 255 255 255;
  --card-foreground: 23 23 23;
  --primary: var(--brand-600);
  --primary-foreground: 255 255 255;
  --secondary: 245 245 245;
  --secondary-foreground: 23 23 23;
  --accent: 245 245 245;
  --accent-foreground: 23 23 23;
  --destructive: 239 68 68;
  --destructive-foreground: 255 255 255;
  --ring: var(--brand-600);
}

.dark {
  /* Dark mode colors */
  --background: 10 10 10;
  --foreground: 250 250 250;
  --muted: 38 38 38;
  --muted-foreground: 163 163 163;
  --border: 38 38 38;
  --input: 38 38 38;
  --card: 10 10 10;
  --card-foreground: 250 250 250;
  --primary: var(--brand-500);
  --primary-foreground: 10 10 10;
  --secondary: 38 38 38;
  --secondary-foreground: 250 250 250;
  --accent: 38 38 38;
  --accent-foreground: 250 250 250;
  --destructive: 239 68 68;
  --destructive-foreground: 255 255 255;
  --ring: var(--brand-500);
}

/* Base styles */
* {
  border-color: rgb(var(--border));
}

body {
  background-color: rgb(var(--background));
  color: rgb(var(--foreground));
  font-feature-settings:
    'rlig' 1,
    'calt' 1;
}

/* Ad placement styles */
.ad-container {
  background-color: rgb(250 250 250);
  border: 1px solid rgb(229 229 229);
  border-radius: 0.5rem;
  overflow: hidden;
}

.dark .ad-container {
  background-color: rgb(23 23 23);
  border-color: rgb(38 38 38);
}

.ad-label {
  font-size: 0.75rem;
  color: rgb(115 115 115);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 500;
  padding: 0.25rem 0.75rem;
}

.dark .ad-label {
  color: rgb(163 163 163);
}

.ad-caption {
  font-size: 0.75rem;
  color: rgb(115 115 115);
  padding: 0.5rem 0.75rem;
}

.dark .ad-caption {
  color: rgb(163 163 163);
}

/* Content width constraint */
.content-width {
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

/* Site width constraint */
.site-width {
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .site-width {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .site-width {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}
