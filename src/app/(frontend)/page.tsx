import { getCurrentSite } from '@/lib/site'
import { getPostsForSite } from '@/lib/posts'
import { AdPlacement, AdPlaceholder } from '@/components/ads/AdPlacement'
import { Button } from '@/components/ui/Button'
import { formatDate } from '@/lib/utils'
import Image from 'next/image'
import Link from 'next/link'
import { Mail } from 'lucide-react'

// Force dynamic rendering for multi-tenant
export const dynamic = 'force-dynamic'

export default async function HomePage() {
  let site
  try {
    site = await getCurrentSite()
  } catch (error) {
    // Log error in development only
    if (process.env.NODE_ENV === 'development') {
      console.error('HomePage: Error fetching current site:', error)
    }

    // Return error page
    return (
      <div className="site-width py-16">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-4 text-red-600">Error</h1>
          <p className="text-muted-foreground">
            There was an error loading the site. Please try again later.
          </p>
          <details className="mt-4 text-left">
            <summary className="cursor-pointer">Error Details</summary>
            <pre className="mt-2 p-4 bg-gray-100 rounded text-sm overflow-auto">
              {error instanceof Error ? error.message : 'Unknown error'}
            </pre>
          </details>
        </div>
      </div>
    )
  }

  if (!site) {
    return (
      <div className="site-width py-16">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-4">Site Not Found</h1>
          <p className="text-muted-foreground">
            This domain is not configured. Please check your site settings.
          </p>
        </div>
      </div>
    )
  }

  // Get featured posts and recent posts
  let featuredPosts: any[] = []
  let recentPosts: any[] = []

  try {
    const featuredResult = await getPostsForSite(site.id, {
      featured: true,
      status: 'published',
      limit: 1,
    })
    const recentResult = await getPostsForSite(site.id, { status: 'published', limit: 6 })

    featuredPosts = featuredResult || []
    recentPosts = recentResult || []
  } catch (error) {
    console.error('Error fetching posts for site:', error)
    // Continue with empty arrays during build
    featuredPosts = []
    recentPosts = []
  }

  const heroPost = featuredPosts[0]
  const otherPosts = recentPosts.filter((post) => post.id !== heroPost?.id).slice(0, 5)

  return (
    <div>
      {/* Header Ad Placement */}
      <div className="site-width py-4">
        <AdPlaceholder placement="header" className="max-w-4xl mx-auto" />
      </div>

      {/* Hero Section - Apple Newsroom Style */}
      <section className="site-width py-16">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left side - Hero content */}
          <div className="space-y-6">
            <h1 className="text-5xl lg:text-6xl font-bold leading-tight">
              {site.seoDefaults?.title || `Welcome to ${site.name}`}
            </h1>
            <p className="text-xl text-muted-foreground leading-relaxed">
              {site.seoDefaults?.description || 'Stay updated with the latest news and insights.'}
            </p>

            {/* Email capture form */}
            <div className="flex flex-col sm:flex-row gap-3 max-w-md">
              <div className="flex-1">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="w-full px-4 py-3 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring"
                />
              </div>
              <Button size="lg" className="px-6">
                <Mail className="h-4 w-4 mr-2" />
                Subscribe
              </Button>
            </div>
          </div>

          {/* Right side - Hero image or featured post */}
          <div className="relative">
            {heroPost ? (
              <Link href={`/${heroPost.slug}`} className="block group">
                <div className="relative overflow-hidden rounded-2xl bg-muted">
                  {heroPost.featuredImage ? (
                    <Image
                      src={heroPost.featuredImage.url}
                      alt={heroPost.featuredImage.alt || heroPost.title}
                      width={600}
                      height={400}
                      className="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  ) : (
                    <div className="w-full h-80 bg-gradient-to-br from-brand-500 to-brand-700 flex items-center justify-center">
                      <span className="text-white text-6xl font-bold">
                        {heroPost.title.charAt(0)}
                      </span>
                    </div>
                  )}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                  <div className="absolute bottom-6 left-6 right-6 text-white">
                    <h2 className="text-2xl font-bold mb-2 line-clamp-2">{heroPost.title}</h2>
                    {heroPost.excerpt && (
                      <p className="text-white/90 line-clamp-2">{heroPost.excerpt}</p>
                    )}
                    <div className="flex items-center mt-3 text-sm text-white/80">
                      <span>{formatDate(heroPost.publishedAt)}</span>
                      {heroPost.readingTime && (
                        <>
                          <span className="mx-2">•</span>
                          <span>{heroPost.readingTime} min read</span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </Link>
            ) : (
              <div className="w-full h-80 bg-gradient-to-br from-brand-500 to-brand-700 rounded-2xl flex items-center justify-center">
                <div className="text-center text-white">
                  <h2 className="text-3xl font-bold mb-2">{site.name}</h2>
                  <p className="text-white/90">Coming Soon</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Recent Posts Grid */}
      {otherPosts.length > 0 && (
        <section className="site-width py-16">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-3xl font-bold">Latest Stories</h2>
            <Button variant="outline" asChild>
              <Link href="/posts">View All</Link>
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {otherPosts.map((post, index) => (
              <article key={post.id} className="group">
                <Link href={`/${post.slug}`} className="block">
                  <div className="relative overflow-hidden rounded-lg bg-muted mb-4">
                    {post.featuredImage ? (
                      <Image
                        src={post.featuredImage.url}
                        alt={post.featuredImage.alt || post.title}
                        width={400}
                        height={250}
                        className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                    ) : (
                      <div className="w-full h-48 bg-gradient-to-br from-brand-500/20 to-brand-700/20 flex items-center justify-center">
                        <span className="text-brand-600 text-4xl font-bold">
                          {post.title.charAt(0)}
                        </span>
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <h3 className="text-xl font-semibold group-hover:text-brand-600 transition-colors line-clamp-2">
                      {post.title}
                    </h3>
                    {post.excerpt && (
                      <p className="text-muted-foreground line-clamp-3">{post.excerpt}</p>
                    )}
                    <div className="flex items-center text-sm text-muted-foreground">
                      <span>{formatDate(post.publishedAt)}</span>
                      {post.readingTime && (
                        <>
                          <span className="mx-2">•</span>
                          <span>{post.readingTime} min read</span>
                        </>
                      )}
                    </div>
                  </div>
                </Link>

                {/* Ad placement after every 2nd post */}
                {index === 1 && (
                  <div className="mt-8">
                    <AdPlaceholder placement="content" />
                  </div>
                )}
              </article>
            ))}
          </div>
        </section>
      )}

      {/* Sidebar Ad Placement */}
      <div className="site-width py-8">
        <div className="max-w-sm mx-auto">
          <AdPlaceholder placement="sidebar" />
        </div>
      </div>
    </div>
  )
}
