import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  // Drop the existing column if it exists (to fix type mismatch)
  await db.execute(sql`
    ALTER TABLE tenants 
    DROP COLUMN IF EXISTS theme_config_font_family;
  `)

  // Add the column as VARCHAR to store the actual string values
  // Payload stores select field values as the option values: 'inter', 'geist', 'ibm-plex-serif'
  await db.execute(sql`
    ALTER TABLE tenants 
    ADD COLUMN theme_config_font_family VARCHAR DEFAULT 'inter';
  `)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
    ALTER TABLE tenants 
    DROP COLUMN IF EXISTS theme_config_font_family;
  `)
}
