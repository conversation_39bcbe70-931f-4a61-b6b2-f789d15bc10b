import { s3Storage } from '@payloadcms/storage-s3'
import { vercelPostgresAdapter } from '@payloadcms/db-vercel-postgres'
import { payloadCloudPlugin } from '@payloadcms/payload-cloud'
import { lexicalEditor } from '@payloadcms/richtext-lexical'
import { multiTenantPlugin } from '@payloadcms/plugin-multi-tenant'
import { seoPlugin } from '@payloadcms/plugin-seo'
import type {
  GenerateTitle,
  GenerateDescription,
  GenerateURL,
  GenerateImage,
} from '@payloadcms/plugin-seo/types'
import path from 'path'
import { buildConfig } from 'payload'
import { fileURLToPath } from 'url'
import sharp from 'sharp'

import { Users } from './collections/Users'
import { Media } from './collections/Media'
import { Posts } from './collections/Posts'
import { Pages } from './collections/Pages'
import { Authors } from './collections/Authors'
import { Categories } from './collections/Categories'
import { Tags } from './collections/Tags'
import { Tenants } from './collections/Tenants'
import { AdCampaigns } from './collections/AdCampaigns'
import { AdCreatives } from './collections/AdCreatives'
import { PricingRules } from './collections/PricingRules'
import { Navigation } from './globals/Navigation'
import { slugifyFilename } from './utils/slugify'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

// Typed SEO generate functions following best practices
// Now using all available parameters from plugin interface
const generateTitle: GenerateTitle = ({
  doc,
  collectionSlug,
  globalSlug,
  collectionConfig,
  globalConfig,
  locale,
  req,
}) => {
  // Handle globals
  if (globalSlug) {
    switch (globalSlug) {
      case 'navigation':
        return 'Navigation Settings'
      default:
        return 'Global Settings'
    }
  }

  // Handle collections
  if (!doc?.title && !doc?.name) return 'Untitled'

  switch (collectionSlug) {
    case 'posts':
      return doc.title
    case 'pages':
      return doc.title
    case 'categories':
      return `${doc.name} - Category`
    case 'authors':
      return `${doc.name} - Author`
    case 'tags':
      return `${doc.name} - Tag`
    default:
      return doc.title || doc.name || 'Untitled'
  }
}

const generateDescription: GenerateDescription = ({
  doc,
  collectionSlug,
  globalSlug,
  collectionConfig,
  globalConfig,
  locale,
  req,
}) => {
  // Try custom description first, then excerpt, then generate from content
  if (doc?.meta?.description) return doc.meta.description
  if (doc?.excerpt) return doc.excerpt
  if (doc?.description) return doc.description

  // Handle globals
  if (globalSlug) {
    switch (globalSlug) {
      case 'navigation':
        return 'Manage your site navigation menus and structure.'
      default:
        return 'Global configuration settings for your website.'
    }
  }

  // Collection-specific description generation
  switch (collectionSlug) {
    case 'posts':
      return doc.excerpt || `Read ${doc.title} for the latest insights and updates.`
    case 'pages':
      return doc.excerpt || `Learn more about ${doc.title} and discover valuable information.`
    case 'categories':
      return `Explore all content in the ${doc.name} category. Find articles, posts, and resources.`
    case 'authors':
      return `Discover content by ${doc.name}. Read their latest articles and insights.`
    case 'tags':
      return `Content tagged with ${doc.name}. Explore related articles and posts.`
    default:
      return `Learn more about ${doc.title || doc.name || 'this content'}.`
  }
}

const generateURL: GenerateURL = ({
  doc,
  collectionSlug,
  globalSlug,
  collectionConfig,
  globalConfig,
  locale,
  req,
}) => {
  // Handle globals
  if (globalSlug) {
    switch (globalSlug) {
      case 'navigation':
        return '/admin/globals/navigation'
      default:
        return '/admin/globals'
    }
  }

  // Collection-specific URL patterns
  if (!doc?.slug) return '/preview'

  switch (collectionSlug) {
    case 'posts':
      return `/${doc.slug}`
    case 'pages':
      return `/${doc.slug}`
    case 'categories':
      return `/category/${doc.slug}`
    case 'authors':
      return `/author/${doc.slug}`
    case 'tags':
      return `/tag/${doc.slug}`
    default:
      return `/${doc.slug}`
  }
}

const generateImage: GenerateImage = ({
  doc,
  collectionSlug,
  globalSlug,
  collectionConfig,
  globalConfig,
  locale,
  req,
}) => {
  // Try meta image first, then featured image, then collection-specific defaults
  if (doc?.meta?.image) return doc.meta.image
  if (doc?.featuredImage) return doc.featuredImage
  if (doc?.image) return doc.image
  if (doc?.avatar) return doc.avatar // For authors

  // Handle globals
  if (globalSlug) {
    switch (globalSlug) {
      case 'navigation':
        return undefined // Navigation doesn't typically have images
      default:
        return undefined
    }
  }

  // Could return a default image URL here if needed
  return undefined
}

// Environment validation for production
if (!process.env.POSTGRES_URL || !process.env.PAYLOAD_SECRET) {
  throw new Error('Missing required environment variables: POSTGRES_URL and PAYLOAD_SECRET')
}
// Using vercelPostgresAdapter for Neon database (Vercel Postgres)

export default buildConfig({
  admin: {
    user: Users.slug,
    importMap: {
      baseDir: path.resolve(dirname),
    },
  },
  collections: [
    Users,
    Media,
    Posts,
    Pages,
    Authors,
    Categories,
    Tags,
    Tenants,
    // Sponsorship Platform
    AdCampaigns,
    AdCreatives,
    PricingRules,
  ],
  globals: [Navigation],
  editor: lexicalEditor(),
  secret: process.env.PAYLOAD_SECRET || '',
  typescript: {
    outputFile: path.resolve(dirname, 'payload-types.ts'),
  },
  db: vercelPostgresAdapter({
    pool: {
      connectionString: process.env.DATABASE_URL || process.env.POSTGRES_URL || '',
    },
    // Enable schema pushing in development for automatic table creation
    push: process.env.NODE_ENV === 'development',
  }),
  sharp,
  plugins: [
    payloadCloudPlugin(),
    seoPlugin({
      // Enable SEO for all relevant collections
      // Note: tabbedUI currently has compatibility issues with globals in our setup
      collections: ['posts', 'pages', 'categories', 'authors', 'tags'],
      uploadsCollection: 'media',

      // Enable tabbed UI for better admin experience on collections
      // Organizes content fields in "General" tab and SEO fields in "SEO" tab
      tabbedUI: true,

      // Custom interface names for better TypeScript generation
      interfaceName: 'SEOMeta',

      // Use our typed generate functions
      generateTitle,
      generateDescription,
      generateURL,
      generateImage,

      // Add custom fields for enhanced SEO
      fields: ({ defaultFields }) => [
        ...defaultFields,
        {
          name: 'canonicalUrl',
          type: 'text',
          label: 'Canonical URL',
          validate: (value: string | null | undefined) => {
            if (!value) return true // Optional field

            try {
              new URL(value)
              return true
            } catch {
              return 'Please enter a valid URL (e.g., https://example.com/page)'
            }
          },
          admin: {
            description:
              'Override the canonical URL for this content. Use for cross-posting, canonical redirects, or preferred domain versions. Must be a complete URL with protocol.',
            placeholder: 'https://example.com/preferred-url',
            condition: (data, siblingData) => {
              // Show for all content, not just published
              return true
            },
          },
        },
        {
          name: 'noIndex',
          type: 'checkbox',
          label: 'No Index',
          admin: {
            description: 'Prevent search engines from indexing this content',
          },
        },
        {
          name: 'noFollow',
          type: 'checkbox',
          label: 'No Follow',
          admin: {
            description: 'Prevent search engines from following links on this page',
          },
        },
        {
          name: 'maxSnippet',
          type: 'number',
          label: 'Max Snippet Length',
          defaultValue: -1,
          admin: {
            description:
              'Maximum number of characters in search snippet (-1 = no limit, 0 = no snippet)',
            step: 1,
            condition: (data, siblingData) => {
              // Only show when not noIndex
              return !siblingData?.noIndex
            },
          },
        },
        {
          name: 'maxImagePreview',
          type: 'select',
          label: 'Max Image Preview',
          defaultValue: 'large',
          options: [
            {
              label: 'None',
              value: 'none',
            },
            {
              label: 'Standard',
              value: 'standard',
            },
            {
              label: 'Large',
              value: 'large',
            },
          ],
          admin: {
            description: 'Maximum size of image preview in search results',
            condition: (data, siblingData) => {
              return !siblingData?.noIndex
            },
          },
        },
        {
          name: 'maxVideoPreview',
          type: 'number',
          label: 'Max Video Preview (seconds)',
          defaultValue: -1,
          admin: {
            description:
              'Maximum duration of video preview in seconds (-1 = no limit, 0 = no preview)',
            step: 1,
            condition: (data, siblingData) => {
              return !siblingData?.noIndex
            },
          },
        },
        {
          name: 'noImageIndex',
          type: 'checkbox',
          label: 'No Image Index',
          admin: {
            description: 'Prevent search engines from indexing images on this page',
          },
        },
        {
          name: 'noArchive',
          type: 'checkbox',
          label: 'No Archive',
          admin: {
            description: 'Prevent search engines from showing cached links',
          },
        },
        {
          name: 'noSnippet',
          type: 'checkbox',
          label: 'No Snippet',
          admin: {
            description: 'Prevent search engines from showing any text snippet',
          },
        },
      ],
    }),
    multiTenantPlugin({
      collections: {
        posts: {},
        pages: {},
        authors: {},
        categories: {},
        tags: {},
        media: {
          // Allow media to be shared across sites
          useTenantAccess: false,
        },
      },
      // Function to determine if a user has access to all sites (super admin)
      userHasAccessToAllTenants: (user) => {
        return user?.roles?.includes('super-admin') || false
      },
      // Customize the site selector label
      tenantSelectorLabel: 'Select Site',

      // Enable debug mode to see site fields in admin
      debug: process.env.NODE_ENV === 'development',
    }),
    // Cloudflare R2 Storage Configuration - R2 ONLY, no local storage
    s3Storage({
      collections: {
        media: {
          // Dynamic prefix from database record will be used for tenant-specific folders
          // This allows tenant-specific folder structure: timeeting/filename.svg
          disableLocalStorage: true, // Explicitly disable local storage - files stored ONLY in R2
          generateFileURL: ({ filename, prefix }) => {
            // Use filename directly from the parameters

            // Handle null/undefined filename gracefully
            if (!filename) {
              console.warn('🚨 generateFileURL called with null/undefined filename:', {
                filename,
                prefix,
              })
              return '' // Return empty string or handle as needed
            }

            // Generate clean, SEO-friendly URLs using tenant-specific domains
            let baseUrl = 'http://localhost:3000' // fallback for development

            // Get tenant from prefix
            const tenant = prefix

            // In production, map tenant to their custom domains
            if (process.env.NODE_ENV === 'production' || process.env.VERCEL_URL) {
              if (tenant === 'pynions') {
                baseUrl = process.env.NEXT_PUBLIC_PYNIONS_URL || 'https://pynions.com'
              } else if (tenant === 'timeeting') {
                baseUrl = process.env.NEXT_PUBLIC_TIMEETING_URL || 'https://timeeting.com'
              } else if (tenant === 'justpricing') {
                baseUrl = process.env.NEXT_PUBLIC_JUSTPRICING_URL || 'https://justpricing.com'
              } else {
                // Fallback to admin domain for unknown tenants
                baseUrl = process.env.NEXT_PUBLIC_ADMIN_URL || 'https://ship.craftled.com'
              }
            } else {
              // Development: use localhost with tenant subdomains if available
              baseUrl = process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:3000'
            }

            // Return clean URL: domain.com/media/slugified-file-name.png
            return `${baseUrl}/media/${slugifyFilename(filename)}`
          },
        },
      },
      bucket: process.env.S3_BUCKET || '',
      config: {
        credentials: {
          accessKeyId: process.env.S3_ACCESS_KEY_ID || '',
          secretAccessKey: process.env.S3_SECRET_ACCESS_KEY || '',
        },
        region: process.env.S3_REGION || 'auto',
        endpoint: process.env.S3_ENDPOINT || '',
        forcePathStyle: true, // Required for Cloudflare R2
        // Cloudflare R2 best practices for checksum compatibility
        requestChecksumCalculation: 'WHEN_REQUIRED',
        responseChecksumValidation: 'WHEN_REQUIRED',
      },
    }),
  ],
})
