import { getPayload } from 'payload'
import config from '@payload-config'

export interface TenantInfo {
  id: string
  name: string
  slug: string
  domain: string
  domains?: Array<{ domain: string }>
  isActive: boolean
  logo?: any
  favicon?: any
  seoDefaults?: {
    title?: string
    description?: string
  }
  themeConfig?: any
}

// Domain to tenant slug mapping for development and production
const DOMAIN_TENANT_MAP: Record<string, string> = {
  // Development domains
  'localhost:3000': 'timeeting',
  'timeeting.localhost:3000': 'timeeting',
  'pynions.localhost:3000': 'pynions',

  // Production domains
  'timeeting.com': 'timeeting',
  'www.timeeting.com': 'timeeting',
  'pynions.com': 'pynions',
  'www.pynions.com': 'pynions',
  'ship.craftled.com': 'timeeting', // Admin domain defaults to timeeting
}

/**
 * Resolve tenant slug from domain name
 */
export function resolveTenantFromDomain(domain: string): string | null {
  // Remove port for production domains
  const cleanDomain = domain.replace(/:\d+$/, '')

  // Check exact match first
  if (DOMAIN_TENANT_MAP[domain]) {
    return DOMAIN_TENANT_MAP[domain]
  }

  // Check without port
  if (DOMAIN_TENANT_MAP[cleanDomain]) {
    return DOMAIN_TENANT_MAP[cleanDomain]
  }

  // Default fallback
  return 'timeeting'
}

/**
 * Get tenant information by slug
 */
export async function getTenantBySlug(slug: string): Promise<TenantInfo | null> {
  try {
    const payload = await getPayload({ config })

    const tenants = await payload.find({
      collection: 'tenants',
      where: {
        slug: {
          equals: slug,
        },
      },
      limit: 1,
    })

    if (tenants.docs.length === 0) {
      // Return default tenant for development
      return {
        id: 'default',
        name: 'Timeeting',
        slug: 'timeeting',
        domain: 'localhost:3000',
        isActive: true,
        seoDefaults: {
          title: 'Timeeting - Time Management Solutions',
          description:
            'Discover the best time management tools and techniques to boost your productivity.',
        },
      }
    }

    const tenant = tenants.docs[0] as any

    return {
      id: String(tenant.id),
      name: tenant.name,
      slug: tenant.slug,
      domain: tenant.domain,
      domains: tenant.domains,
      isActive: Boolean(tenant.isActive),
      logo: tenant.logo,
      favicon: tenant.favicon,
      themeConfig: tenant.themeConfig,
      seoDefaults: tenant.seoDefaults,
    }
  } catch (error) {
    console.error('Error fetching tenant by slug:', error)
    return null
  }
}

/**
 * Get tenant information by domain
 */
export async function getTenantByDomain(domain: string): Promise<TenantInfo | null> {
  const tenantSlug = resolveTenantFromDomain(domain)
  if (!tenantSlug) return null

  return getTenantBySlug(tenantSlug)
}

/**
 * Get all active tenants
 */
export async function getAllTenants(): Promise<TenantInfo[]> {
  try {
    const payload = await getPayload({ config })

    const tenants = await payload.find({
      collection: 'tenants',
      where: {
        isActive: {
          equals: true,
        },
      },
      limit: 100,
    })

    return tenants.docs.map((tenant: any) => ({
      id: tenant.id,
      name: tenant.name,
      slug: tenant.slug,
      domain: tenant.domain,
      domains: tenant.domains,
      isActive: tenant.isActive,
      themeConfig: tenant.themeConfig,
      seoDefaults: tenant.seoDefaults,
    }))
  } catch (error) {
    console.error('Error fetching all tenants:', error)
    return []
  }
}
