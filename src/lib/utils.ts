import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: string | Date) {
  return new Intl.DateTimeFormat('en-US', {
    month: 'long',
    day: 'numeric',
    year: 'numeric',
  }).format(new Date(date))
}

export function readingTime(text: string) {
  const wordsPerMinute = 200
  const words = text.trim().split(/\s+/).length
  const time = Math.ceil(words / wordsPerMinute)
  return time
}
