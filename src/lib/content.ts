import { getPayload } from 'payload'
import config from '@payload-config'
import { TenantInfo } from './tenant'

export interface ContentItem {
  id: string
  title: string
  slug: string
  content?: any
  excerpt?: string
  publishedAt?: string
  updatedAt?: string
  status: 'draft' | 'published' | 'scheduled' | 'archived'
  tenant: string
  author?: any
  category?: any // Single category as per Posts collection
  tags?: any[]
  featuredImage?: any
  seo?: {
    title?: string
    description?: string
    keywords?: string
  }
}

export interface CategoryItem {
  id: string
  name: string
  slug: string
  description?: string
  tenant: string
}

export interface TagItem {
  id: string
  name: string
  slug: string
  description?: string
  tenant: string
}

export interface AuthorItem {
  id: string
  name: string
  slug: string
  email?: string
  bio?: string
  avatar?: any
  role: 'author' | 'editor' | 'admin'
  socialLinks?: {
    twitter?: string
    linkedin?: string
    website?: string
  }
  isActive: boolean
  tenant: string
}

/**
 * Get content by slug and type for a specific tenant
 */
export async function getContentBySlug(
  slug: string,
  tenant: TenantInfo,
  type: 'posts' | 'pages' = 'posts',
): Promise<ContentItem | null> {
  try {
    const payload = await getPayload({ config })

    const content = await payload.find({
      collection: type,
      where: {
        and: [
          {
            slug: {
              equals: slug,
            },
          },
          {
            tenant: {
              equals: tenant.id,
            },
          },
          {
            status: {
              equals: 'published',
            },
          },
        ],
      },
      limit: 1,
      depth: 2, // Include related data
    })

    if (content.docs.length === 0) {
      return null
    }

    const item = content.docs[0] as any

    return {
      id: String(item.id),
      title: item.title,
      slug: item.slug,
      content: item.content,
      excerpt: item.excerpt,
      publishedAt: item.publishedAt,
      updatedAt: item.updatedAt,
      status: item.status as 'draft' | 'published' | 'scheduled' | 'archived',
      tenant:
        typeof item.tenant === 'object' ? String(item.tenant?.id || '') : String(item.tenant || ''),
      author: item.author,
      category: item.category,
      tags: item.tags,
      featuredImage: item.featuredImage,
      seo: item.seo,
    }
  } catch (error) {
    console.error(`Error fetching ${type} by slug:`, error)
    return null
  }
}

/**
 * Get category by slug for a specific tenant
 */
export async function getCategoryBySlug(
  slug: string,
  tenant: TenantInfo,
): Promise<CategoryItem | null> {
  try {
    const payload = await getPayload({ config })

    const categories = await payload.find({
      collection: 'categories',
      where: {
        and: [
          {
            slug: {
              equals: slug,
            },
          },
          {
            tenant: {
              equals: tenant.id,
            },
          },
        ],
      },
      limit: 1,
    })

    if (categories.docs.length === 0) {
      return null
    }

    const category = categories.docs[0] as any

    return {
      id: category.id,
      name: category.name,
      slug: category.slug,
      description: category.description,
      tenant: category.tenant,
    }
  } catch (error) {
    console.error('Error fetching category by slug:', error)
    return null
  }
}

/**
 * Get tag by slug for a specific tenant
 */
export async function getTagBySlug(slug: string, tenant: TenantInfo): Promise<TagItem | null> {
  try {
    const payload = await getPayload({ config })

    const tags = await payload.find({
      collection: 'tags',
      where: {
        and: [
          {
            slug: {
              equals: slug,
            },
          },
          {
            tenant: {
              equals: tenant.id,
            },
          },
        ],
      },
      limit: 1,
    })

    if (tags.docs.length === 0) {
      return null
    }

    const tag = tags.docs[0] as any

    return {
      id: String(tag.id),
      name: tag.name,
      slug: tag.slug,
      description: tag.description || undefined,
      tenant:
        typeof tag.tenant === 'object' ? String(tag.tenant?.id || '') : String(tag.tenant || ''),
    }
  } catch (error) {
    console.error('Error fetching tag by slug:', error)
    return null
  }
}

/**
 * Get author by slug for a specific tenant
 */
export async function getAuthorBySlug(
  slug: string,
  tenant: TenantInfo,
): Promise<AuthorItem | null> {
  try {
    const payload = await getPayload({ config })

    const authors = await payload.find({
      collection: 'authors',
      where: {
        and: [
          {
            slug: {
              equals: slug,
            },
          },
          {
            tenant: {
              equals: tenant.id,
            },
          },
        ],
      },
      limit: 1,
    })

    if (authors.docs.length === 0) {
      return null
    }

    const author = authors.docs[0]
    return {
      id: String(author.id),
      name: author.name,
      slug: author.slug,
      email: author.email || undefined,
      bio: author.bio || undefined,
      avatar: author.avatar,
      role: (author.role as 'author' | 'editor' | 'admin') || 'author',
      socialLinks: author.socialLinks
        ? {
            twitter: author.socialLinks.twitter || undefined,
            linkedin: author.socialLinks.linkedin || undefined,
            website: author.socialLinks.website || undefined,
          }
        : {},
      isActive: Boolean(author.isActive),
      tenant:
        typeof author.tenant === 'object'
          ? String(author.tenant?.id || '')
          : String(author.tenant || ''),
    }
  } catch (error) {
    console.error('Error fetching author by slug:', error)
    return null
  }
}

/**
 * Get posts for a category
 */
export async function getPostsByCategory(
  categoryId: string,
  tenant: TenantInfo,
  limit: number = 10,
  page: number = 1,
): Promise<{ docs: ContentItem[]; totalDocs: number; totalPages: number }> {
  try {
    const payload = await getPayload({ config })

    const posts = await payload.find({
      collection: 'posts',
      where: {
        and: [
          {
            category: {
              equals: categoryId,
            },
          },
          {
            tenant: {
              equals: tenant.id,
            },
          },
          {
            status: {
              equals: 'published',
            },
          },
        ],
      },
      limit,
      page,
      sort: '-publishedAt',
      depth: 2,
    })

    return {
      docs: posts.docs.map((post: any) => ({
        id: post.id,
        title: post.title,
        slug: post.slug,
        content: post.content,
        excerpt: post.excerpt,
        publishedAt: post.publishedAt,
        status: post.status,
        tenant: post.tenant,
        author: post.author,
        categories: post.categories,
        tags: post.tags,
        featuredImage: post.featuredImage,
        seo: post.seo,
      })),
      totalDocs: posts.totalDocs,
      totalPages: posts.totalPages,
    }
  } catch (error) {
    console.error('Error fetching posts by category:', error)
    return { docs: [], totalDocs: 0, totalPages: 0 }
  }
}

/**
 * Get posts for a tag
 */
export async function getPostsByTag(
  tagId: string,
  tenant: TenantInfo,
  limit: number = 10,
  page: number = 1,
): Promise<{ docs: ContentItem[]; totalDocs: number; totalPages: number }> {
  try {
    const payload = await getPayload({ config })

    const posts = await payload.find({
      collection: 'posts',
      where: {
        and: [
          {
            tags: {
              in: [tagId],
            },
          },
          {
            tenant: {
              equals: tenant.id,
            },
          },
          {
            status: {
              equals: 'published',
            },
          },
        ],
      },
      limit,
      page,
      sort: '-publishedAt',
      depth: 2,
    })

    return {
      docs: posts.docs.map((post: any) => ({
        id: post.id,
        title: post.title,
        slug: post.slug,
        content: post.content,
        excerpt: post.excerpt,
        publishedAt: post.publishedAt,
        status: post.status,
        tenant: post.tenant,
        author: post.author,
        categories: post.categories,
        tags: post.tags,
        featuredImage: post.featuredImage,
        seo: post.seo,
      })),
      totalDocs: posts.totalDocs,
      totalPages: posts.totalPages,
    }
  } catch (error) {
    console.error('Error fetching posts by tag:', error)
    return { docs: [], totalDocs: 0, totalPages: 0 }
  }
}

/**
 * Get posts for an author
 */
export async function getPostsByAuthor(
  authorId: string,
  tenant: TenantInfo,
  limit: number = 10,
  page: number = 1,
): Promise<{ docs: ContentItem[]; totalDocs: number; totalPages: number }> {
  try {
    const payload = await getPayload({ config })

    const posts = await payload.find({
      collection: 'posts',
      where: {
        and: [
          {
            author: {
              equals: authorId,
            },
          },
          {
            tenant: {
              equals: tenant.id,
            },
          },
          {
            status: {
              equals: 'published',
            },
          },
        ],
      },
      limit,
      page,
      sort: '-publishedAt',
      depth: 2,
    })

    return {
      docs: posts.docs.map((post: any) => ({
        id: post.id,
        title: post.title,
        slug: post.slug,
        content: post.content,
        excerpt: post.excerpt,
        publishedAt: post.publishedAt,
        status: post.status,
        tenant: post.tenant,
        author: post.author,
        categories: post.categories,
        tags: post.tags,
        featuredImage: post.featuredImage,
        seo: post.seo,
      })),
      totalDocs: posts.totalDocs,
      totalPages: posts.totalPages,
    }
  } catch (error) {
    console.error('Error fetching posts by author:', error)
    return { docs: [], totalDocs: 0, totalPages: 0 }
  }
}
