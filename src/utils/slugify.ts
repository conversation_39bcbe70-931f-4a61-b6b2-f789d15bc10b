/**
 * Slugify a filename for SEO-friendly URLs
 * Converts "CleanShot 2025-08-11 at <EMAIL>" to "cleanshot-2025-08-11-at-14-20-37-2x.png"
 */
export function slugifyFilename(filename: string | null | undefined): string {
  // Handle null/undefined filename
  if (!filename) {
    return 'unnamed-file'
  }

  // Split filename into name and extension
  const lastDotIndex = filename.lastIndexOf('.')
  const name = lastDotIndex > 0 ? filename.substring(0, lastDotIndex) : filename
  const extension = lastDotIndex > 0 ? filename.substring(lastDotIndex) : ''

  // Slugify the name part (convert dots to hyphens in the name)
  const slugifiedName = name
    .toLowerCase()
    .replace(/\s+/g, '-') // Replace spaces with hyphens first
    .replace(/[@().]/g, '-') // Replace @, (, ), . with hyphens
    .replace(/[^a-z0-9-]/g, '-') // Replace remaining non-alphanumeric chars with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, '') // Remove leading/trailing hyphens

  // Return name + extension (keep the dot in extension)
  return slugifiedName + extension
}

/**
 * Reverse slugification to help find original filenames
 * Converts "cleanshot-2025-08-11-at-14-20-37-2x.png" back to searchable terms
 */
export function deSlugifyFilename(slugifiedFilename: string): string {
  return slugifiedFilename
    .replace(/-/g, ' ') // Replace hyphens with spaces
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .trim()
}
