import type { GlobalConfig } from 'payload'

export const Navigation: GlobalConfig = {
  slug: 'navigation',
  label: 'Navigation',
  admin: {
    group: 'Configuration',
  },
  fields: [
    {
      name: 'primaryNavigation',
      type: 'array',
      label: 'Primary Navigation',
      fields: [
        {
          name: 'label',
          type: 'text',
          label: 'Label',
          required: true,
        },
        {
          name: 'type',
          type: 'radio',
          label: 'Link Type',
          options: [
            {
              label: 'Internal Page',
              value: 'internal',
            },
            {
              label: 'External URL',
              value: 'external',
            },
            {
              label: 'Category',
              value: 'category',
            },
          ],
          defaultValue: 'internal',
          required: true,
        },
        {
          name: 'internalPage',
          type: 'relationship',
          relationTo: 'pages',
          label: 'Internal Page',
          admin: {
            condition: (data, siblingData) => siblingData?.type === 'internal',
          },
        },
        {
          name: 'externalUrl',
          type: 'text',
          label: 'External URL',
          admin: {
            condition: (data, siblingData) => siblingData?.type === 'external',
          },
        },
        {
          name: 'category',
          type: 'relationship',
          relationTo: 'categories',
          label: 'Category',
          admin: {
            condition: (data, siblingData) => siblingData?.type === 'category',
          },
        },
        {
          name: 'openInNewTab',
          type: 'checkbox',
          label: 'Open in New Tab',
          defaultValue: false,
        },
        {
          name: 'subItems',
          type: 'array',
          label: 'Sub Navigation',
          maxRows: 10,
          fields: [
            {
              name: 'label',
              type: 'text',
              label: 'Label',
              required: true,
            },
            {
              name: 'type',
              type: 'radio',
              label: 'Link Type',
              options: [
                {
                  label: 'Internal Page',
                  value: 'internal',
                },
                {
                  label: 'External URL',
                  value: 'external',
                },
                {
                  label: 'Category',
                  value: 'category',
                },
              ],
              defaultValue: 'internal',
              required: true,
            },
            {
              name: 'internalPage',
              type: 'relationship',
              relationTo: 'pages',
              label: 'Internal Page',
              admin: {
                condition: (data, siblingData) => siblingData?.type === 'internal',
              },
            },
            {
              name: 'externalUrl',
              type: 'text',
              label: 'External URL',
              admin: {
                condition: (data, siblingData) => siblingData?.type === 'external',
              },
            },
            {
              name: 'category',
              type: 'relationship',
              relationTo: 'categories',
              label: 'Category',
              admin: {
                condition: (data, siblingData) => siblingData?.type === 'category',
              },
            },
            {
              name: 'openInNewTab',
              type: 'checkbox',
              label: 'Open in New Tab',
              defaultValue: false,
            },
          ],
        },
      ],
      maxRows: 8,
    },
    {
      name: 'footerNavigation',
      type: 'array',
      label: 'Footer Navigation',
      fields: [
        {
          name: 'label',
          type: 'text',
          label: 'Label',
          required: true,
        },
        {
          name: 'type',
          type: 'radio',
          label: 'Link Type',
          options: [
            {
              label: 'Internal Page',
              value: 'internal',
            },
            {
              label: 'External URL',
              value: 'external',
            },
          ],
          defaultValue: 'internal',
          required: true,
        },
        {
          name: 'internalPage',
          type: 'relationship',
          relationTo: 'pages',
          label: 'Internal Page',
          admin: {
            condition: (data, siblingData) => siblingData?.type === 'internal',
          },
        },
        {
          name: 'externalUrl',
          type: 'text',
          label: 'External URL',
          admin: {
            condition: (data, siblingData) => siblingData?.type === 'external',
          },
        },
      ],
      maxRows: 12,
    },
    // Manual SEO fields for Navigation (since tabbedUI has issues with globals)
    {
      name: 'seo',
      type: 'group',
      label: 'SEO Settings',
      fields: [
        {
          name: 'title',
          type: 'text',
          label: 'Meta Title',
          admin: {
            description: 'SEO title for navigation management page',
          },
        },
        {
          name: 'description',
          type: 'textarea',
          label: 'Meta Description',
          admin: {
            description: 'SEO description for navigation management page',
          },
        },
      ],
    },
  ],
}
