# Bun configuration file
# https://bun.sh/docs/runtime/bunfig

[install]
# Use isolated installs (similar to pnpm) for better dependency isolation
# linker = "isolated"

# Prevent lockfile changes in production
frozenLockfile = false

# Save text-based lockfile (default since Bun v1.2)
saveTextLockfile = true

# Install dev dependencies by default
dev = true

# Install optional dependencies by default
optional = true

# Install peer dependencies by default
peer = true

# Concurrency settings for better performance
concurrentScripts = 16
