
# Database (Vercel Postgres - Neon)
DATABASE_URL=**********************************************************
POSTGRES_URL=**********************************************************
# Note: These are automatically provided by Vercel when you connect a Neon database

# Payload CMS
PAYLOAD_SECRET=your-secret-key-here
PAYLOAD_PUBLIC_SERVER_URL=http://localhost:3000

# Cloudflare R2 Storage (optional)
S3_ENDPOINT=https://[account-id].r2.cloudflarestorage.com
S3_REGION=auto
S3_BUCKET=your-bucket-name
S3_ACCESS_KEY_ID=your-access-key
S3_SECRET_ACCESS_KEY=your-secret-key

# Umami Analytics (optional)
NEXT_PUBLIC_UMAMI_WEBSITE_ID=your-website-id
NEXT_PUBLIC_UMAMI_SCRIPT_URL=https://your-umami-domain/script.js

# Next.js
NEXT_PUBLIC_PAYLOAD_URL=http://localhost:3000