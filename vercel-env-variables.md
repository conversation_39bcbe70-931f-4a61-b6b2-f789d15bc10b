# Vercel Environment Variables Configuration

## Required Environment Variables for Production

### Database (Vercel Postgres - Neon)

```
DATABASE_URL=postgres://[USER]:[PASSWORD]@[HOST]/[DATABASE]?sslmode=require
POSTGRES_URL=postgres://[USER]:[PASSWORD]@[HOST]/[DATABASE]?sslmode=require
```

Note: These are automatically provided by Ver<PERSON> when you connect a Neon database.

### PayloadCMS Core

```
PAYLOAD_SECRET=your-super-secret-key-32-chars-min
PAYLOAD_CONFIG_PATH=src/payload.config.ts
```

### Cloudflare R2 Storage

```
S3_ENDPOINT=https://[ACCOUNT_ID].r2.cloudflarestorage.com
S3_REGION=auto
S3_BUCKET=your-bucket-name
S3_ACCESS_KEY_ID=your-r2-access-key-id
S3_SECRET_ACCESS_KEY=your-r2-secret-access-key
```

### Multi-Tenant Configuration

```
# Base URLs for each tenant
NEXT_PUBLIC_TIMEETING_URL=https://timeeting.com
NEXT_PUBLIC_PYNIONS_URL=https://pynions.com

# Admin URL (can be subdomain or path)
NEXT_PUBLIC_ADMIN_URL=https://ship.craftled.com
```

### Umami Analytics (External)

```
NEXT_PUBLIC_UMAMI_SCRIPT_URL=https://your-umami-domain.com/script.js
NEXT_PUBLIC_UMAMI_TIMEETING_ID=your-timeeting-website-id
NEXT_PUBLIC_UMAMI_PYNIONS_ID=your-pynions-website-id
```

### Optional: Email (Resend)

```
RESEND_API_KEY=your-resend-api-key
RESEND_FROM_EMAIL=<EMAIL>
```

### Optional: Preview/Development

```
NEXT_PUBLIC_VERCEL_ENV=production
VERCEL_URL=auto-populated-by-vercel
```

## Environment Variable Setup in Vercel

1. **Go to Vercel Dashboard** → Your Project → Settings → Environment Variables
2. **Add each variable** with appropriate environment scope:
   - **Production**: Live domains
   - **Preview**: Git branches/PRs
   - **Development**: Local development

3. **Important Notes:**
   - `NEXT_PUBLIC_*` variables are exposed to the client
   - Database and API keys should NOT have `NEXT_PUBLIC_` prefix
   - Vercel automatically provides `VERCEL_URL` and `VERCEL_ENV`

## Pull Environment Variables Locally

Since Vercel is the single source of truth for environment variables:

```bash
# Link your local project to Vercel project
npx vercel link

# Pull environment variables from Vercel to .env.local
npx vercel env pull .env.local
```

This will create/update your local `.env.local` file with the production environment variables from Vercel.
